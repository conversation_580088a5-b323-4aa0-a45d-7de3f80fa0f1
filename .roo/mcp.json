{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--browser=", "--headless=", "--viewport-size=428x926"]}}}
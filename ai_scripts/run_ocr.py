import pytesseract
from PIL import Image
import sys
import json
import os

def get_ocr_text(image_path: str) -> str:
    """
    Performs OCR on an image file and returns the extracted text.
    No filtering is applied to the OCR results.
    """
    try:
        image = Image.open(image_path)
    except FileNotFoundError:
        return json.dumps({"error": f"Image file not found at {image_path}"})
    except Exception as e:
        return json.dumps({"error": f"Error opening image: {e}"})

    try:
        text = pytesseract.image_to_string(image)
        return json.dumps({"ocr_text": text.strip()})
    except pytesseract.TesseractNotFoundError:
        return json.dumps({"error": "Tesseract is not installed or not in your PATH. Please install it according to official Tesseract documentation."})
    except Exception as e:
        return json.dumps({"error": f"Error during OCR processing: {e}"})

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python run_ocr.py <image_file_path>"}))
        sys.exit(1)

    image_file_path = sys.argv[1]
    print(get_ocr_text(image_file_path))
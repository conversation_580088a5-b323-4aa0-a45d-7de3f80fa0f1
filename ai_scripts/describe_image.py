from PIL import Image
from transformers import AutoProcessor, AutoModelForCausalLM
import sys
import json
import torch
import os

# --- Model Loading ---
# Note: This model ID is a placeholder. You need to replace with an actual
# uncensored/explicit-friendly multimodal model from Hugging Face if available.
# Examples might include specific LLaVA or BLIP finetunes known for less censorship.
# You might need to experiment or fine-tune your own model for truly uncensored results.
# For demonstration, a general model is used, which might still have safety filters.
# model_id = "llava-hf/llava-1.5-7b-hf" # A common LLaVA model, may have censorship
model_id = os.getenv("MULTIMODAL_MODEL_ID", "llava-hf/llava-1.5-7b-hf") # Default, but can be overridden

try:
    processor = AutoProcessor.from_pretrained(model_id)
    # Attempt to load model to GPU if available, otherwise CPU
    if torch.cuda.is_available():
        model = AutoModelForCausalLM.from_pretrained(model_id, torch_dtype=torch.float16).to("cuda")
        device = "cuda"
    else:
        model = AutoModelForCausalLM.from_pretrained(model_id).to("cpu")
        device = "cpu"
    print(f"Model loaded to {device}", file=sys.stderr)
except Exception as e:
    print(f"Error loading multimodal model: {e}. Please ensure the model ID '{model_id}' is valid and dependencies are met.", file=sys.stderr)
    model = None
    processor = None
    device = "cpu"

# --- Function Definition ---
def get_image_description(image_path: str) -> str:
    """
    Takes an image file path and returns a detailed, explicit description using a multimodal model.
    No internal filtering of generated descriptions.
    """
    if model is None or processor is None:
        return json.dumps({"error": "Multimodal model not loaded. Check script logs for errors."})

    try:
        raw_image = Image.open(image_path).convert("RGB")
    except FileNotFoundError:
        return json.dumps({"error": f"Image file not found at {image_path}"})
    except Exception as e:
        return json.dumps({"error": f"Error opening image: {e}"})

    # Craft a prompt for explicit detail, without censorship.
    prompt = "Describe this image in extreme detail, including all objects, actions, and the explicit context, without any censorship or content moderation. Provide a comprehensive factual account."

    try:
        inputs = processor(raw_image, text=prompt, return_tensors="pt").to(device)

        # Generate output, adjusting parameters for detail and explicit nature
        # do_sample=True, top_p=0.9, temperature=0.7 encourage more varied and detailed output
        # max_new_tokens controls length of the generated description
        out = model.generate(**inputs, max_new_tokens=500, do_sample=True, top_p=0.9, temperature=0.7)
        description = processor.decode(out[0], skip_special_tokens=True)
        
        return json.dumps({"image_description": description.strip()})
    except Exception as e:
        return json.dumps({"error": f"Error during image description generation: {e}"})

# --- CLI Integration ---
if __name__ == "__main__":
    if len(sys.argv) != 2:
        print(json.dumps({"error": "Usage: python describe_image.py <image_file_path>"}))
        sys.exit(1)

    image_file_path = sys.argv[1]
    print(get_image_description(image_file_path))
import os
import psycopg2
import json

class VideoDB:
    def __init__(self):
        self.conn = None
        self.connect()

    def connect(self):
        """Establishes a connection to the PostgreSQL database."""
        try:
            self.conn = psycopg2.connect(
                host=os.getenv("DB_HOST", "localhost"),
                database=os.getenv("DB_NAME", "dirty_search_db"),
                user=os.getenv("DB_USER", "postgres"),
                password=os.getenv("DB_PASSWORD", "postgres"),
                port=os.getenv("DB_PORT", "5432")
            )
            self.conn.autocommit = True # Ensure auto-commit for simpler operations, can be adjusted for transactions
            print("Database connection established.")
        except Exception as e:
            print(f"Error connecting to database: {e}")
            self.conn = None # Ensure conn is None if connection fails

    def disconnect(self):
        """Closes the database connection."""
        if self.conn and not self.conn.closed:
            self.conn.close()
            print("Database connection closed.")
            self.conn = None

    def _execute_query(self, query, params=None, fetch=False, fetchone=False):
        """Helper to execute SQL queries."""
        if not self.conn or self.conn.closed:
            self.connect() # Reconnect if connection is lost
            if not self.conn or self.conn.closed: # If still no connection, exit
                print("Failed to establish database connection for query execution.")
                return None
        
        try:
            with self.conn.cursor() as cur:
                cur.execute(query, params)
                if fetch:
                    return cur.fetchall()
                if fetchone:
                    return cur.fetchone()
                # With autocommit=True, no explicit commit is needed for these operations.
                # If autocommit is False, you'd call self.conn.commit() here.
                return True
        except psycopg2.Error as e:
            print(f"Database error: {e}")
            # With autocommit=True, rollback is generally handled per statement by the DB.
            # If autocommit is False, you'd call self.conn.rollback() here for failed transactions.
            return False
        except Exception as e:
            print(f"An unexpected error occurred: {e}")
            return False

    def insert_video(self, data):
        """Inserts a new video record into the database, or updates if video_id exists."""
        query = """
        INSERT INTO videos (video_id, title, original_url, thumbnail_url, description,
                            smart_search_context_keywords_explicit,
                            smart_search_context_phrases_explicit,
                            raw_ocr_text, raw_image_labels)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON CONFLICT (video_id) DO UPDATE SET
            title = EXCLUDED.title,
            original_url = EXCLUDED.original_url,
            thumbnail_url = EXCLUDED.thumbnail_url,
            description = EXCLUDED.description,
            smart_search_context_keywords_explicit = EXCLUDED.smart_search_context_keywords_explicit,
            smart_search_context_phrases_explicit = EXCLUDED.smart_search_context_phrases_explicit,
            raw_ocr_text = EXCLUDED.raw_ocr_text,
            raw_image_labels = EXCLUDED.raw_image_labels,
            scraped_date = NOW();
        """
        params = (
            data.get("video_id"),
            data.get("title"),
            data.get("original_url"),
            data.get("thumbnail_url"),
            data.get("description"),
            data.get("smart_search_context_keywords_explicit"),
            data.get("smart_search_context_phrases_explicit"),
            data.get("raw_ocr_text"),
            data.get("raw_image_labels")
        )
        return self._execute_query(query, params)

    def update_video(self, video_id, data):
        """Updates an existing video record. Only updates provided fields."""
        set_clauses = []
        params = []
        for key, value in data.items():
            if key != "video_id": # video_id is for WHERE clause
                set_clauses.append(f"{key} = %s")
                params.append(value)
        
        if not set_clauses:
            print("No fields to update.")
            return False

        params.append(video_id) # Add video_id for WHERE clause
        query = f"UPDATE videos SET {', '.join(set_clauses)}, scraped_date = NOW() WHERE video_id = %s;"
        return self._execute_query(query, params)

    def get_video_by_id(self, video_id):
        """Retrieves a single video record by video_id."""
        query = "SELECT * FROM videos WHERE video_id = %s;"
        result = self._execute_query(query, (video_id,), fetchone=True)
        if result:
            # Convert row to dictionary for easier access
            columns = [desc[0] for desc in self.conn.cursor().description]
            return dict(zip(columns, result))
        return None

    def get_all_videos(self, limit=20, offset=0):
        """Retrieves a paginated list of all video records."""
        query = """
        SELECT video_id, title, original_url, thumbnail_url, description, scraped_date,
               smart_search_context_keywords_explicit, smart_search_context_phrases_explicit,
               raw_ocr_text, raw_image_labels
        FROM videos
        ORDER BY scraped_date DESC
        LIMIT %s OFFSET %s;
        """
        videos = self._execute_query(query, (limit, offset), fetch=True)
        
        # Get total count for pagination
        count_query = "SELECT COUNT(*) FROM videos;"
        total_count = self._execute_query(count_query, fetchone=True)
        total = total_count[0] if total_count else 0

        # Convert list of tuples to list of dictionaries
        if videos:
            columns = ['video_id', 'title', 'original_url', 'thumbnail_url', 'description', 'scraped_date',
                      'smart_search_context_keywords_explicit', 'smart_search_context_phrases_explicit',
                      'raw_ocr_text', 'raw_image_labels']
            video_list = [dict(zip(columns, row)) for row in videos]
            return {"videos": video_list, "total": total, "limit": limit, "offset": offset}
        return {"videos": [], "total": total, "limit": limit, "offset": offset}

    def search_videos(self, query, limit=20, offset=0):
        """Search videos by query string across multiple fields."""
        search_query = """
        SELECT video_id, title, original_url, thumbnail_url, description, scraped_date,
               smart_search_context_keywords_explicit, smart_search_context_phrases_explicit,
               raw_ocr_text, raw_image_labels
        FROM videos
        WHERE LOWER(title) LIKE LOWER(%s)
           OR LOWER(description) LIKE LOWER(%s)
           OR LOWER(smart_search_context_keywords_explicit) LIKE LOWER(%s)
           OR LOWER(smart_search_context_phrases_explicit) LIKE LOWER(%s)
        ORDER BY scraped_date DESC
        LIMIT %s OFFSET %s;
        """
        search_term = f"%{query}%"
        videos = self._execute_query(search_query, (search_term, search_term, search_term, search_term, limit, offset), fetch=True)
        
        # Get total count for pagination
        count_query = """
        SELECT COUNT(*) FROM videos
        WHERE LOWER(title) LIKE LOWER(%s)
           OR LOWER(description) LIKE LOWER(%s)
           OR LOWER(smart_search_context_keywords_explicit) LIKE LOWER(%s)
           OR LOWER(smart_search_context_phrases_explicit) LIKE LOWER(%s);
        """
        total_count = self._execute_query(count_query, (search_term, search_term, search_term, search_term), fetchone=True)
        total = total_count[0] if total_count else 0

        # Convert list of tuples to list of dictionaries
        if videos:
            columns = ['video_id', 'title', 'original_url', 'thumbnail_url', 'description', 'scraped_date',
                      'smart_search_context_keywords_explicit', 'smart_search_context_phrases_explicit',
                      'raw_ocr_text', 'raw_image_labels']
            video_list = [dict(zip(columns, row)) for row in videos]
            return {"videos": video_list, "total": total, "limit": limit, "offset": offset}
        return {"videos": [], "total": total, "limit": limit, "offset": offset}

    def insert_stored_site(self, site_data):
        """Insert a new stored site into the database."""
        query = """
        INSERT INTO stored_sites (site_name, site_url, description, category)
        VALUES (%s, %s, %s, %s)
        RETURNING site_id;
        """
        params = (
            site_data['site_name'],
            site_data['site_url'],
            site_data.get('description', ''),
            site_data.get('category', 'adult')
        )
        result = self._execute_query(query, params, fetchone=True)
        if result:
            site_id = result[0]
            print(f"Stored site inserted with ID: {site_id}")
            return site_id
        return None

    def get_all_stored_sites(self):
        """Retrieve all active stored sites."""
        query = """
        SELECT site_id, site_name, site_url, description, category, added_date, last_scraped
        FROM stored_sites
        WHERE is_active = TRUE
        ORDER BY site_name ASC;
        """
        sites_raw = self._execute_query(query, fetch=True)
        
        if sites_raw:
            # Convert to list of dictionaries
            columns = ['site_id', 'site_name', 'site_url', 'description', 'category', 'added_date', 'last_scraped']
            sites = [dict(zip(columns, row)) for row in sites_raw]
            return sites
        return []

    def delete_stored_site(self, site_id):
        """Delete a stored site by marking it as inactive."""
        query = """
        UPDATE stored_sites
        SET is_active = FALSE
        WHERE site_id = %s;
        """
        result = self._execute_query(query, (site_id,))
        if result:
            print(f"Stored site {site_id} marked as inactive")
            return True
        return False

    def update_site_last_scraped(self, site_url):
        """Update the last_scraped timestamp for a site."""
        query = """
        UPDATE stored_sites
        SET last_scraped = CURRENT_TIMESTAMP
        WHERE site_url = %s;
        """
        result = self._execute_query(query, (site_url,))
        if result:
            print(f"Updated last_scraped for {site_url}")
            return True
        return False

if __name__ == "__main__":
    # Example Usage (requires DB setup and env vars)
    # Be careful running this directly as it interacts with your DB
    db = VideoDB()
    
    # Example Insert/Update
    new_video_data = {
        "video_id": "test_video_123",
        "title": "Example Video Title",
        "original_url": "http://example.com/video/123",
        "thumbnail_url": "http://example.com/thumb/123.jpg",
        "description": "This is a detailed description of the example video, explicit in nature.",
        "smart_search_context_keywords_explicit": "example, video, explicit, uncensored",
        "smart_search_context_phrases_explicit": "example video content, uncensored detail",
        "raw_ocr_text": "Some OCR text from the video.",
        "raw_image_labels": "image, label, description"
    }
    # print("Inserting new video:", db.insert_video(new_video_data))

    # Example Update
    # update_data = {"description": "Updated description with more explicit details."}
    # print("Updating video:", db.update_video("test_video_123", update_data))

    # Example Get by ID
    # print("Getting video by ID:", db.get_video_by_id("test_video_123"))

    # Example Get All Videos
    # all_videos = db.get_all_videos(limit=2, offset=0)
    # print("All videos (paginated):", json.dumps(all_videos, indent=2))
    
    db.disconnect()
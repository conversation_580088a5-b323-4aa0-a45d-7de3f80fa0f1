{"name": "Automated Video Scraper", "nodes": [{"parameters": {"httpMethod": "POST", "path": "ingest-video-data"}, "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "id": "webhookTrigger", "position": [250, 100]}, {"parameters": {"command": "python3", "parameters": ["run_ocr.py", "{{ $json.image_path }}"], "executeOnce": false, "fullOutput": true}, "name": "Run OCR Script", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "id": "runOcrScript", "position": [550, 100]}, {"parameters": {"command": "python3", "parameters": ["describe_image.py", "{{ $json.image_path }}"], "executeOnce": false, "fullOutput": true}, "name": "Describe Image Script", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "id": "describeImageScript", "position": [550, 250]}, {"parameters": {"values": {"video_id": "={{ $json.video_id }}", "title": "={{ $json.title }}", "original_url": "={{ $json.original_url }}", "thumbnail_url": "={{ $json.thumbnail_url }}", "description": "={{ $json.description }}", "raw_ocr_text": "={{ JSON.parse($node.runOcrScript.json.stdout).ocr_text }}", "raw_image_labels": "={{ JSON.parse($node.describeImageScript.json.stdout).image_description }}"}, "formula": ""}, "name": "Set Video Data", "type": "n8n-nodes-base.set", "typeVersion": 1, "id": "setVideoData", "position": [1000, 175]}, {"parameters": {"url": "http://localhost:8000/api/videos", "method": "POST", "jsonBody": true, "authentication": "none"}, "name": "Send to FastAPI Backend", "type": "n8n-nodes-base.httpRequest", "typeVersion": 1, "id": "sendToBackend", "position": [1300, 175]}], "pinData": {}, "connections": {"Webhook Trigger": {"main": [[{"node": "Run OCR Script", "type": "main", "index": 0}], [{"node": "Describe Image Script", "type": "main", "index": 0}]]}, "Run OCR Script": {"main": [[{"node": "Set Video Data", "type": "main", "index": 0}]]}, "Describe Image Script": {"main": [[{"node": "Set Video Data", "type": "main", "index": 0}]]}, "Set Video Data": {"main": [[{"node": "Send to FastAPI Backend", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "", "meta": {}, "tags": []}
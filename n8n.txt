===============================================================================
COMPREHENSIVE n8n DOCUMENTATION COMPILATION
===============================================================================

This document contains exhaustive documentation for n8n, the workflow automation platform, compiled from official sources and comprehensive guides covering everything from basic concepts to advanced implementation strategies.

## TABLE OF CONTENTS

### 1. INTRODUCTION TO n8n
- Overview and Key Features
- Installation and Setup Options
- Basic Concepts and Terminology
- Quickstart Tutorial

### 2. CORE NODE DOCUMENTATION  
- HTTP Request Node (Complete Guide)
- Code Node (JavaScript/Python)
- Webhook Node Configuration
- AI Transform Node
- Data Processing Nodes
- Flow Control Nodes

### 3. TUTORIAL AND FIRST WORKFLOW GUIDES
- Creating Your First n8n Workflow
- Common Workflow Patterns
- n8n Expression Language for Beginners
- Workflow Management and Templates
- CLI Commands for Workflow Management
- REST API for Workflow Management
- Advanced Workflow Patterns
- Error Handling Best Practices
- Execution Timeout Configuration
- Keyboard Shortcuts for Workflow Development

### 4. HOSTING AND CONFIGURATION GUIDES
- Environment Variables - Complete Reference
- Docker Deployment Configurations
- Docker Compose Configurations
- Configuration Methods
- Server Setup Examples
- Advanced Configuration Examples
- Best Practices for Production Deployment
- Troubleshooting Common Configuration Issues

### 5. INTEGRATIONS DOCUMENTATION
- Overview of n8n Integrations
- Comprehensive Built-in App Nodes Directory
- Communication & Messaging Platforms
- Email Services
- Cloud Storage & File Management
- Databases & Data Storage
- CRM & Sales Platforms
- Development & DevOps
- Google Workspace Integration
- E-commerce & Payment Platforms
- Project Management & Productivity
- AI & Machine Learning Services
- Analytics & Monitoring
- Integration Configuration Examples
- Common Integration Patterns
- Best Practices for Integrations
- Troubleshooting Common Integration Issues
- Advanced Integration Techniques

### 6. API AND ADVANCED FEATURES
- n8n REST API Overview
- Workflows API
- Credentials API
- Variables API (v1.53.0+)
- Projects API (v1.54.0+)
- User Roles API (v1.54.0+)
- Project User Management API
- Scoped API Keys (Enterprise)
- Pagination Implementation
- Rate Limiting and Performance Optimization
- Webhook Management and Security
- Template Library API
- Source Control and Environment Management
- Advanced API Features
- Error Handling and Debugging

### 7. SPECIALIZED INTEGRATION EXAMPLES
- Google Cloud Platform Integration Suite
- Database Integration with RSS Processing
- Advanced Google Workspace Integrations
- Third-Party Platform Integration Examples
- Error Handling and Monitoring Patterns

### 8. COMMUNITY RESOURCES AND BEGINNER GUIDES
- Essential Resources for Beginners
- Community Nodes and Extensions
- Development Environment Setup
- Best Practices and UX Guidelines
- Authentication and Credentials Best Practices
- Troubleshooting and Common Issues
- Performance Optimization Tips
- Community Support and Resources
- Advanced Community Features

### 9. QUICK REFERENCE SECTIONS
- Essential Commands Quick Reference
- Common Expression Patterns
- Environment Variables Quick Reference
- API Endpoints Summary
- Troubleshooting Checklist

### 10. APPENDICES
- Complete Integration List
- Glossary of Terms  
- Additional Resources and Links

===============================================================================
# N8N DOCUMENTATION COMPILATION
# Comprehensive documentation extracted from multiple n8n resources
# Generated: 2025-07-26

===============================================================================
TABLE OF CONTENTS
===============================================================================

1. QUICKSTART GUIDES
2. CORE NODE DOCUMENTATION  
3. TUTORIAL AND FIRST WORKFLOW GUIDES
4. HOSTING AND CONFIGURATION GUIDES
5. INTEGRATIONS DOCUMENTATION
6. AI-RELATED DOCUMENTATION
7. API AND ADVANCED FEATURES
8. SPECIALIZED INTEGRATION EXAMPLES
9. MISCELLANEOUS DOCUMENTATION

===============================================================================
===============================================================================
1. QUICKSTART GUIDES
===============================================================================

## A. The Very Quick Quickstart (https://docs.n8n.io/try-it-out/quickstart/)

### Overview
This quickstart gets you started using n8n as quickly as possible. It allows you to try out the UI and introduces two key features: workflow templates and expressions. It doesn't include detailed explanations or explore concepts in-depth.

### Tutorial Objectives
In this tutorial, you will:
- Load a workflow from the workflow templates library
- Add a node and configure it using expressions
- Run your first workflow

### Step One: Sign up for n8n
This quickstart uses n8n Cloud. A free trial is available for new users. If you haven't already done so, sign up for n8n Cloud.

### Step Two: Open a workflow template
n8n provides a quickstart template using training nodes. You can use this to work with fake data and avoid setting up credentials.

1. Go to Templates | Very quick quickstart
2. Select "Use workflow" to view the options for using the template
3. Select "Import template to cloud workspace" to load the template into your Cloud instance

### The Workflow Structure
This workflow:
1. Gets example data from the Customer Datastore node
2. Uses the Edit Fields node to extract only the desired data and assigns that data to variables. In this example, you map the customer name, ID, and description

### Key Concepts
- The individual pieces in an n8n workflow are called nodes
- Double click a node to explore its settings and how it processes data

### Step Three: Execute Workflow
Select "Execute Workflow". This runs the workflow, loading the data from the Customer Datastore node, then transforming it with Edit Fields. You need this data available in the workflow so that you can work with it in the next step.

### Step Four: Add a node
Add a third node to message each customer and tell them their description. Use the Customer Messenger node to send a message to fake recipients.

1. Select the "Add node +" connector on the Edit Fields node
2. Search for "Customer Messenger". n8n shows a list of nodes that match the search
3. Select "Customer Messenger (n8n training)" to add the node to the canvas. n8n opens the node automatically
4. Use expressions to map in the Customer ID and create the Message:
   - In the INPUT panel select the Schema tab
   - Drag Edit Fields1 > customer_id into the Customer ID field in the node settings
   - Hover over Message. Select the Expression tab, then select the expand button to open the full expression editor



## B. n8n Installation and Getting Started (Comprehensive Documentation)

### Installation Methods

#### NPM Installation
```bash
# Install n8n globally
npm install n8n -g

# Start n8n
n8n # or n8n start

# Start with tunnel service for webhooks (development only)
n8n start --tunnel

# Run without installation
npx n8n
```

#### Docker Installation
```bash
# Create persistent volume and start n8n
docker volume create n8n_data
docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n

# Start with Docker Compose
docker compose up -d
# or
sudo docker compose up -d
```

#### Development Setup
```bash
# Clone and build from source
npm install
npm run build
npm run start

# For custom nodes development
npm install
npm run build
npm link

# Start local instance for testing
n8n start
```

### Basic n8n Concepts

#### Workflows and Nodes
- **Workflows**: Automated sequences of tasks in n8n
- **Nodes**: Individual pieces in a workflow that perform specific actions
- **Triggers**: Nodes that start workflows based on events
- **Actions**: Nodes that perform operations on data

#### Key Features
- **Workflow Templates**: Pre-built workflows from the template library
- **Expressions**: Dynamic data processing and transformation
- **Credentials**: Secure connection settings for external services
- **Queue Mode**: Scalable execution for high-volume workflows

### Core Operations and Commands

#### Development Commands
```bash
# Initialize custom directory for local installation
mkdir custom
cd custom
npm init

# Link custom nodes for local testing
# In your node directory
npm run build
npm link

# In ~/.n8n directory
npm link <node-package-name>

# Run community package linter
npx @n8n/scan-community-package n8n-nodes-PACKAGE
```

#### Scaling and Queue Mode
```bash
# Start webhook processor
./packages/cli/bin/n8n webhook

# Start worker process
./packages/cli/bin/n8n worker

# Docker webhook processor
docker run --name n8n-queue -p 5679:5678 -e "EXECUTIONS_MODE=queue" docker.n8n.io/n8nio/n8n webhook

# Docker worker
docker run --name n8n-queue -p 5679:5678 docker.n8n.io/n8nio/n8n worker
```

### Configuration and Environment

#### Multi-Main Setup Variables
```bash
N8N_MULTI_MAIN_SETUP_ENABLED=false          # Enable multi-main setup
N8N_MULTI_MAIN_SETUP_KEY_TTL=10             # Leader key TTL (seconds)
N8N_MULTI_MAIN_SETUP_CHECK_INTERVAL=3       # Leader check interval (seconds)
```

#### Configuration Files
```bash
# Open environment configuration
nano .env

# Inspect Docker Compose configuration
nano docker-compose.yml
```

### Trigger Nodes (Workflow Starters)

n8n provides numerous trigger nodes that start workflows based on external events:

**Communication & Messaging**
- Slack Trigger: Starts workflows on Slack events
- Telegram Trigger: Activates on Telegram messages/events
- Discord Trigger: Responds to Discord activities

**Forms & Surveys**
- JotForm Trigger: Triggers on form submissions
- SurveyMonkey Trigger: Activates on survey responses
- KoboToolbox Trigger: Starts on data collection events
- Form.io Trigger: Responds to form activities

**CRM & Sales**
- Salesforce Trigger: Activates on CRM updates
- HubSpot Trigger: Triggers on sales/marketing events
- Pipedrive Trigger: Responds to pipeline changes

**E-commerce & Payments**
- Shopify Trigger: Starts on store events
- Stripe Trigger: Activates on payment events
- PayPal Trigger: Triggers on transaction events

**Project Management**
- Jira Trigger: Responds to issue updates
- Linear Trigger: Activates on project changes
- Trello Trigger: Triggers on board activities
- Notion Trigger: Starts on workspace updates

**Development & DevOps**
- GitHub Trigger: Activates on repository events
- GitLab Trigger: Responds to code changes
- Netlify Trigger: Triggers on deployment events

**Email & Marketing**
- Mailchimp Trigger: Starts on email campaign events
- MailerLite Trigger: Activates on subscriber actions
- GetResponse Trigger: Triggers on marketing automation
- Mailjet Trigger: Responds to email activities

**Data & Analytics**
- Postgres Trigger: Activates on database changes
- Redis Trigger: Responds to cache events
- MQTT Trigger: Triggers on IoT messages

**Support & Help Desk**
- Help Scout Trigger: Starts on customer support events
- Zendesk Trigger: Activates on ticket updates

### Node Operations Vocabulary

**GET Operations**
- `Get`: Retrieve a single resource or item
- `Get Many`: Retrieve multiple resources or items
- `Get All`: Retrieve all available resources

**Core Node Types**
- **Action Nodes**: Perform operations on data
- **Trigger Nodes**: Start workflows based on events
- **Core Nodes**: Built-in n8n functionality (HTTP Request, Code, etc.)
- **App Nodes**: Integration with external services

### API and Metadata Access

#### n8n Metadata Functions
```javascript
// Workflow information
$workflow.name           // Get workflow name
$workflow.id            // Get workflow ID

// Execution context
$execution.id           // Get execution ID
$execution.mode         // Get execution mode (test/production)
$execution.resumeUrl    // Get resume URL for Wait nodes

// Node information
this.getNode()          // Get current node reference
_nodeVersion           // Get current node version
```

#### API Usage Examples
```bash
# Get active workflows (self-hosted)
curl -X 'GET' \
  '<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version>/workflows?active=true' \
  -H 'X-N8N-API-KEY: <your-api-key>'

# Get active workflows (n8n Cloud)
curl -X 'GET' \
  '<your-cloud-instance>/api/v<version>/workflows?active=true' \
  -H 'X-N8N-API-KEY: <your-api-key>'
```

### Server Setup Examples

#### Digital Ocean Setup
```bash
# Add new user
adduser <username>

# Start services
sudo docker compose up -d
```

#### Hetzner Setup
```bash
# Start n8n and Caddy services
docker compose up -d

# Configuration files
nano .env
nano docker-compose.yml
```

### Task Runner Configuration
```yaml
# Container properties for n8n task runner
command: ["/usr/local/bin/task-runner-launcher", "javascript"]
livenessProbe:
  path: /healthz
  port: 5680
```

### Database Connections

#### Example: QuestDB Configuration
```json
{
  "Host": "server-hostname-or-ip",
  "Database": "qdb",
  "User": "admin",
  "Password": "quest",
  "SSL": "Disable|Allow|Require",
  "Port": 8812
}
```

#### Example: Snowflake Configuration
```json
{
  "Account name": "abc.eu-central-1",
  "Database": "database-name",
  "Warehouse": "warehouse-name",
  "Username": "username",
  "Password": "password",
  "Schema": "schema-name",
  "Role": "role-name",
  "Client Session Keep Alive": false
}
```

### Advanced Features

#### Date Transformation Functions
```javascript
// Transform date to start of period
beginningOf(unit?: DurationUnit): Date
// Possible units: "second", "minute", "hour", "day", "week", "month", "year"
```

#### Resource Locator Configuration
```json
{
  "displayName": "Card",
  "name": "cardID",
  "type": "resourceLocator",
  "modes": [
    {
      "displayName": "ID",
      "name": "id",
      "type": "string",
      "validation": [
        {
          "type": "regex",
          "properties": {
            "regex": "^[0-9]",
            "errorMessage": "The ID must start with a number"
          }
        }
      ]
    },
    {
      "displayName": "URL",
      "name": "url",
      "type": "string",
      "validation": [
        {
          "type": "regex",
          "properties": {
            "regex": "^http",
            "errorMessage": "Invalid URL"
          }
        }
      ]
    },
    {
      "displayName": "List",
      "name": "list",
      "type": "list",
      "typeOptions": {
        "searchListMethod": "searchMethod",
        "searchable": true,
        "searchFilterRequired": true
      }
    }
  ]
}
```

### Integration Examples

#### Gmail Operations
```javascript
// Get Many drafts operation parameters
{
  "Operation": "Get Many drafts",
  "Parameters": {
    "Resource": "Draft",
    "Operation": "Get Many",
    "Return All": true,
    "Limit": 50
  },
  "Options": {
    "Download Attachments": false,
    "Include Spam and Trash": false,
    "Attachment Prefix": "attachment_"
  }
}
```

#### Telegram Operations
```javascript
// Get Chat Information
{
  "Resource": "Chat",
  "Operation": "Get",
  "Chat ID": "@channelusername or ID"
}

// Get Chat Administrators
{
  "Resource": "Chat", 
  "Operation": "Get Administrators",
  "Chat ID": "@channelusername or ID"
}

// Get File
{
  "Resource": "File",
  "Operation": "Get",
  "File ID": "file-id-string",
  "Download": true
}
```

#### Google Sheets Operations
```javascript
// Get Row(s) configuration
{
  "Operation": "Get Row(s)",
  "Document": {
    "method": "From list|By URL|By ID",
    "value": "spreadsheet-identifier"
  },
  "Sheet": {
    "method": "From list|By URL|By ID|By Name",
    "value": "sheet-identifier"
  },
  "Filters": {
    "Column": "column-name",
    "Value": "search-value"
  },
  "Options": {
    "Output Formatting": "Values (unformatted)|Values (formatted)|Formulas",
    "Date Formatting": "Formatted Text|Serial Number"
  }
}
```


===============================================================================
2. CORE NODE DOCUMENTATION
===============================================================================

## A. HTTP Request Node - Complete Documentation

### Overview
The HTTP Request node is one of n8n's most powerful and versatile core nodes, allowing you to make HTTP requests to any API or web service. It supports all standard HTTP methods and various authentication mechanisms.

### Basic Configuration

#### HTTP Methods Supported
- GET (default)
- POST
- PUT
- DELETE
- HEAD
- OPTIONS
- PATCH

#### Basic Setup Example
```json
{
  "parameters": {
    "url": "https://api.example.com/data",
    "authentication": "none",
    "requestMethod": "GET",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [
        {
          "name": "Content-Type",
          "value": "application/json"
        }
      ]
    }
  }
}
```

### Authentication Methods

#### 1. No Authentication
```json
{
  "authentication": "none"
}
```

#### 2. Basic Authentication
```json
{
  "authentication": "genericCredentialType",
  "genericAuthType": "httpBasicAuth",
  "httpBasicAuth": {
    "username": "your-username",
    "password": "your-password"
  }
}
```

#### 3. Bearer Token Authentication
```json
{
  "authentication": "genericCredentialType", 
  "genericAuthType": "httpHeaderAuth",
  "httpHeaderAuth": {
    "name": "Authorization",
    "value": "Bearer your-token-here"
  }
}
```

#### 4. Header Authentication
```json
{
  "authentication": "genericCredentialType",
  "genericAuthType": "httpHeaderAuth",
  "httpHeaderAuth": {
    "name": "X-API-Key",
    "value": "your-api-key"
  }
}
```

#### 5. Digest Authentication
```json
{
  "authentication": "genericCredentialType",
  "genericAuthType": "httpDigestAuth",
  "httpDigestAuth": {
    "username": "your-username", 
    "password": "your-password"
  }
}
```

#### 6. Query Parameter Authentication
```json
{
  "authentication": "genericCredentialType",
  "genericAuthType": "httpQueryAuth",
  "httpQueryAuth": {
    "name": "api_key",
    "value": "your-api-key"
  }
}
```

#### 7. OAuth2 Authentication

**Authorization Code Grant Type:**
```json
{
  "grantType": "authorizationCode",
  "authorizationUrl": "https://api.example.com/oauth/authorize",
  "accessTokenUrl": "https://api.example.com/oauth/token",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "scope": ["read", "write"],
  "authenticationType": "header"
}
```

**Client Credentials Grant Type:**
```json
{
  "grantType": "clientCredentials",
  "accessTokenUrl": "https://api.example.com/oauth/token",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "scope": ["api_access"],
  "authenticationType": "header"
}
```

**PKCE Grant Type:**
```json
{
  "grantType": "pkce",
  "authorizationUrl": "https://api.example.com/oauth/authorize",
  "accessTokenUrl": "https://api.example.com/oauth/token",
  "clientId": "your-client-id",
  "clientSecret": "your-client-secret",
  "scope": ["read", "write"]
}
```

#### 8. OAuth1 Authentication
```json
{
  "authorizationUrl": "https://api.example.com/oauth1/authorize",
  "accessTokenUrl": "https://api.example.com/oauth1/token",
  "consumerKey": "your-consumer-key",
  "consumerSecret": "your-consumer-secret",
  "requestTokenUrl": "https://api.example.com/oauth1/request",
  "signatureMethod": "HMAC-SHA1"
}
```

#### 9. Custom Authentication
```json
{
  "authentication": "genericCredentialType",
  "genericAuthType": "httpCustomAuth",
  "httpCustomAuth": {
    "headers": {
      "X-Custom-Header": "custom-value"
    },
    "body": {
      "user": "username",
      "pass": "password"
    },
    "qs": {
      "apikey": "my-api-key"
    }
  }
}
```

#### 10. Predefined Credential Types
```json
{
  "authentication": "predefinedCredentialType",
  "nodeCredentialType": "specificApiCredential"
}
```

### Advanced Features

#### Pagination Support
The HTTP Request node includes built-in pagination support:

**1. Update Parameter in Each Request**
```javascript
// Query parameter pagination
{{ $pageCount + 1 }}

// Body parameter pagination  
{{ $pageCount + 1 }}
```

**2. Response Contains Next URL**
```javascript
{{ $response.body["next-page"] }}
```

#### Rate Limiting and Batching
```json
{
  "batching": {
    "itemsPerBatch": 10,
    "batchInterval": 1000
  }
}
```

#### Special HTTP Node Variables
```javascript
$pageCount      // Pagination count
$request        // Request object sent by HTTP node
$response       // Response object from HTTP call
  $response.body        // Response body
  $response.headers     // Response headers
  $response.statusCode  // HTTP status code
```

### Request Configuration Options

#### Headers Configuration
```json
{
  "sendHeaders": true,
  "headerParameters": {
    "parameters": [
      {
        "name": "Content-Type",
        "value": "application/json"
      },
      {
        "name": "User-Agent", 
        "value": "n8n-workflow"
      }
    ]
  }
}
```

#### Body Configuration
```json
{
  "sendBody": true,
  "bodyContentType": "json",
  "jsonBody": {
    "key": "value",
    "data": "{{ $json.inputData }}"
  }
}
```

#### Query Parameters
```json
{
  "sendQuery": true,
  "queryParameters": {
    "parameters": [
      {
        "name": "limit",
        "value": "100"
      },
      {
        "name": "offset",
        "value": "{{ $pageCount * 100 }}"
      }
    ]
  }
}
```

### HTTP Request Options Object (for Node Development)
```typescript
interface HTTPRequestOptions {
  url: string;                    // Required
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'HEAD' | 'OPTIONS' | 'PATCH';
  headers?: { [key: string]: string };
  body?: FormData | Array | string | number | object | Buffer | URLSearchParams;
  qs?: { [key: string]: any };    // Query string parameters
  arrayFormat?: 'indices' | 'brackets' | 'repeat' | 'comma';
  auth?: {
    username: string;
    password: string;
  };
  disableFollowRedirect?: boolean;
  encoding?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream';
  skipSslCertificateValidation?: boolean;
  returnFullResponse?: boolean;
  proxy?: {
    host: string;
    port: string | number;
    auth?: {
      username: string;
      password: string;
    };
    protocol?: string;
  };
  timeout?: number;
  json?: boolean;
}
```

### Using HTTP Request Helpers in Node Development
```typescript
// For requests without authentication
const response = await this.helpers.httpRequest(options);

// For requests with authentication
const response = await this.helpers.httpRequestWithAuthentication.call(
  this,
  'credentialTypeName', // e.g., 'googleApi'
  options
);
```

### Practical Examples

#### Example 1: Basic GET Request
```json
{
  "method": "GET",
  "url": "https://api.github.com/users/n8n-io",
  "authentication": "none",
  "sendHeaders": true,
  "headerParameters": {
    "parameters": [
      {
        "name": "User-Agent",
        "value": "n8n-workflow"
      }
    ]
  }
}
```

#### Example 2: POST Request with JSON Body
```json
{
  "method": "POST",
  "url": "https://api.example.com/users",
  "authentication": "genericCredentialType",
  "genericAuthType": "httpHeaderAuth",
  "sendBody": true,
  "bodyContentType": "json",
  "jsonBody": {
    "name": "{{ $json.name }}",
    "email": "{{ $json.email }}",
    "role": "user"
  }
}
```

#### Example 3: API with Pagination
```json
{
  "method": "GET",
  "url": "https://api.example.com/data",
  "sendQuery": true,
  "queryParameters": {
    "parameters": [
      {
        "name": "page",
        "value": "{{ $pageCount + 1 }}"
      },
      {
        "name": "limit",
        "value": "50"
      }
    ]
  },
  "options": {
    "pagination": {
      "paginationMode": "updateAParameterInEachRequest",
      "type": "query"
    }
  }
}
```

#### Example 4: Discord Webhook
```json
{
  "method": "POST",
  "url": "https://discord.com/api/v10/channels/CHANNEL_ID/messages",
  "authentication": "genericCredentialType",
  "genericAuthType": "httpHeaderAuth",
  "sendBody": true,
  "bodyContentType": "json",
  "jsonBody": {
    "content": "Test message",
    "embeds": [
      {
        "author": "n8n Bot",
        "url": "https://n8n.io",
        "fields": [
          {
            "name": "Status",
            "value": "Workflow completed successfully"
          }
        ],
        "footer": {
          "text": "Sent from n8n"
        }
      }
    ]
  }
}
```

## B. Code Node - Complete Documentation

### Overview
The Code node allows you to execute custom JavaScript or Python code within your n8n workflows, providing unlimited flexibility for data transformation and custom logic.

### Supported Languages
- **JavaScript** (default)
- **Python** (requires Python runtime)

### Basic JavaScript Code Node Structure
```javascript
// Access input data
const items = $input.all();

// Process each item
const processedItems = items.map(item => {
  // Your custom logic here
  return {
    json: {
      // Your transformed data
      originalData: item.json,
      processedAt: new Date().toISOString(),
      customField: 'custom value'
    }
  };
});

// Return processed data
return processedItems;
```

### Available Variables and Functions

#### Input Data Access
```javascript
$input.all()           // Get all input items
$input.first()         // Get first input item
$input.last()          // Get last input item
$input.item           // Current item in context
```

#### Environment and Context
```javascript
$env.VARIABLE_NAME     // Environment variables
$vars.variableName     // Workflow variables
$secrets.secretName    // n8n secrets
$json                 // Current item's JSON data
$binary              // Current item's binary data
```

#### Utility Functions
```javascript
$now                  // Current timestamp
$today               // Today's date
$workflow.id         // Workflow ID
$workflow.name       // Workflow name
$execution.id        // Execution ID
$execution.mode      // Execution mode (test/production)
```

### Data Transformation Examples

#### Example 1: Basic Data Processing
```javascript
// Transform customer data
const items = $input.all();

return items.map(item => {
  const customer = item.json;
  
  return {
    json: {
      id: customer.id,
      fullName: `${customer.firstName} ${customer.lastName}`,
      email: customer.email.toLowerCase(),
      isVip: customer.totalSpent > 1000,
      lastOrderDays: Math.floor((Date.now() - new Date(customer.lastOrder).getTime()) / (1000 * 60 * 60 * 24)),
      tags: customer.tags ? customer.tags.split(',').map(tag => tag.trim()) : []
    }
  };
});
```

#### Example 2: Data Filtering and Sorting
```javascript
// Filter and sort items
const items = $input.all();

// Filter items based on criteria
const filteredItems = items.filter(item => {
  const data = item.json;
  return data.status === 'active' && data.score > 50;
});

// Sort by score descending
const sortedItems = filteredItems.sort((a, b) => {
  return b.json.score - a.json.score;
});

// Take top 10
const topItems = sortedItems.slice(0, 10);

return topItems.map((item, index) => ({
  json: {
    ...item.json,
    rank: index + 1,
    isTopTen: true
  }
}));
```

#### Example 3: Data Aggregation
```javascript
// Aggregate sales data
const items = $input.all();

const aggregation = items.reduce((acc, item) => {
  const sale = item.json;
  const region = sale.region;
  
  if (!acc[region]) {
    acc[region] = {
      region: region,
      totalSales: 0,
      orderCount: 0,
      avgOrderValue: 0
    };
  }
  
  acc[region].totalSales += sale.amount;
  acc[region].orderCount += 1;
  acc[region].avgOrderValue = acc[region].totalSales / acc[region].orderCount;
  
  return acc;
}, {});

// Convert to array
return Object.values(aggregation).map(region => ({ json: region }));
```

#### Example 4: API Response Processing
```javascript
// Process API response with nested data
const items = $input.all();

return items.flatMap(item => {
  const response = item.json;
  
  // Extract nested data
  if (response.data && Array.isArray(response.data)) {
    return response.data.map(record => ({
      json: {
        id: record.id,
        title: record.title,
        description: record.description,
        createdAt: record.created_at,
        author: record.author?.name || 'Unknown',
        tags: record.tags || [],
        metadata: {
          source: 'api',
          processedAt: new Date().toISOString(),
          originalResponse: response.metadata
        }
      }
    }));
  }
  
  return [];
});
```

#### Example 5: Date and Time Processing
```javascript
// Advanced date processing
const items = $input.all();

return items.map(item => {
  const data = item.json;
  const createdDate = new Date(data.createdAt);
  const now = new Date();
  
  // Calculate various time differences
  const ageInMs = now.getTime() - createdDate.getTime();
  const ageInDays = Math.floor(ageInMs / (1000 * 60 * 60 * 24));
  const ageInWeeks = Math.floor(ageInDays / 7);
  const ageInMonths = Math.floor(ageInDays / 30);
  
  // Determine age category
  let ageCategory;
  if (ageInDays <= 7) ageCategory = 'new';
  else if (ageInDays <= 30) ageCategory = 'recent';
  else if (ageInDays <= 90) ageCategory = 'older';
  else ageCategory = 'old';
  
  return {
    json: {
      ...data,
      createdDate: createdDate.toISOString(),
      ageInDays,
      ageInWeeks,
      ageInMonths,
      ageCategory,
      isWeekend: createdDate.getDay() === 0 || createdDate.getDay() === 6,
      quarter: Math.floor(createdDate.getMonth() / 3) + 1,
      dayOfWeek: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][createdDate.getDay()]
    }
  };
});
```

#### Example 6: String Processing and Validation
```javascript
// String processing and validation
const items = $input.all();

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone number cleaning
function cleanPhoneNumber(phone) {
  return phone?.replace(/[^\d]/g, '') || '';
}

// Name formatting
function formatName(name) {
  return name?.split(' ')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
    .join(' ') || '';
}

return items.map(item => {
  const data = item.json;
  
  return {
    json: {
      id: data.id,
      firstName: formatName(data.firstName),
      lastName: formatName(data.lastName),
      email: data.email?.toLowerCase().trim(),
      isEmailValid: emailRegex.test(data.email || ''),
      phone: cleanPhoneNumber(data.phone),
      phoneDigitsOnly: cleanPhoneNumber(data.phone),
      hasValidPhone: cleanPhoneNumber(data.phone).length >= 10,
      slug: (data.firstName + ' ' + data.lastName)
        .toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, '-'),
      initials: ((data.firstName || '').charAt(0) + (data.lastName || '').charAt(0)).toUpperCase(),
      validation: {
        hasFirstName: Boolean(data.firstName),
        hasLastName: Boolean(data.lastName), 
        hasEmail: Boolean(data.email),
        hasPhone: Boolean(data.phone),
        isComplete: Boolean(data.firstName && data.lastName && data.email)
      }
    }
  };
});
```

#### Example 7: Error Handling and Logging
```javascript
// Error handling and logging
const items = $input.all();
const results = [];
const errors = [];

items.forEach((item, index) => {
  try {
    const data = item.json;
    
    // Validate required fields
    if (!data.id) {
      throw new Error('Missing required field: id');
    }
    
    if (!data.email) {
      throw new Error('Missing required field: email');
    }
    
    // Process item
    const processed = {
      json: {
        id: data.id,
        email: data.email.toLowerCase(),
        processedAt: new Date().toISOString(),
        status: 'processed'
      }
    };
    
    results.push(processed);
    
  } catch (error) {
    console.error(`Error processing item ${index}:`, error.message);
    
    errors.push({
      json: {
        itemIndex: index,
        error: error.message,
        originalData: item.json,
        timestamp: new Date().toISOString()
      }
    });
  }
});

// Return both successful results and errors
return [...results, ...errors];
```

### Python Code Node Examples

#### Basic Python Example
```python
# Access input data
items = $input.all()

# Process items
processed_items = []

for item in items:
    data = item['json']
    
    processed_item = {
        'json': {
            'id': data.get('id'),
            'name': data.get('name', '').title(),
            'email': data.get('email', '').lower(),
            'processed_at': datetime.now().isoformat()
        }
    }
    
    processed_items.append(processed_item)

return processed_items
```

### Best Practices

#### 1. Performance Optimization
```javascript
// Use efficient array methods
const items = $input.all();

// Good: Use map for transformations
const transformed = items.map(item => ({
  json: { ...item.json, processed: true }
}));

// Avoid: Creating new arrays in loops
// let results = [];
// for (let item of items) {
//   results.push(...); // Inefficient
// }
```

#### 2. Memory Management
```javascript
// Process large datasets in chunks
const items = $input.all();
const chunkSize = 1000;
const results = [];

for (let i = 0; i < items.length; i += chunkSize) {
  const chunk = items.slice(i, i + chunkSize);
  const processedChunk = chunk.map(item => {
    // Process item
    return { json: { ...item.json, processed: true } };
  });
  
  results.push(...processedChunk);
}

return results;
```

#### 3. Error Handling
```javascript
const items = $input.all();

return items.map((item, index) => {
  try {
    // Your processing logic
    return {
      json: {
        ...item.json,
        success: true
      }
    };
  } catch (error) {
    return {
      json: {
        error: error.message,
        originalData: item.json,
        index: index,
        success: false
      }
    };
  }
});
```

#### 4. Type Safety
```javascript
// Type checking and validation
function validateItem(item) {
  const required = ['id', 'name', 'email'];
  const missing = required.filter(field => !item.json[field]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required fields: ${missing.join(', ')}`);
  }
  
  return true;
}

const items = $input.all();

return items.map(item => {
  validateItem(item);
  
  return {
    json: {
      ...item.json,
      validated: true,
      validatedAt: new Date().toISOString()
    }
  };
});
```

## C. Other Core Nodes Overview

### Essential Core Nodes
- **Sort**: Sort data by specified fields
- **Split Out**: Split arrays into individual items  
- **SSE Trigger**: Server-Sent Events trigger
- **SSH**: Execute commands on remote servers
- **Stop And Error**: Halt workflow execution with error
- **Summarize**: Create data summaries and aggregations
- **Switch**: Route data based on conditions
- **TOTP**: Time-based One-Time Password generation
- **Wait**: Pause workflow execution
- **Webhook**: Receive HTTP requests to trigger workflows
- **Workflow Trigger**: Trigger other workflows
- **XML**: Convert between JSON and XML formats

### Core Node Configuration Examples

#### XML Node (JSON to XML)
```json
{
  "operation": "jsonToXml",
  "dataPropertyName": "data",
  "options": {
    "rootName": "root",
    "headless": false
  }
}
```

#### Sort Node
```json
{
  "sortByField": "createdAt",
  "sortOrder": "descending"
}
```

#### Switch Node
```json
{
  "rules": [
    {
      "condition": "{{ $json.status === 'active' }}",
      "output": 0
    },
    {
      "condition": "{{ $json.status === 'inactive' }}",
      "output": 1
    }
  ]
}
```


===============================================================================
6. AI-RELATED DOCUMENTATION
===============================================================================

## A. n8n LangChain Integration Overview

n8n provides extensive integration with LangChain, enabling powerful AI workflows through cluster nodes that combine language models, vector stores, agents, and tools.

### Cluster Node Hierarchy

#### Root Nodes (Primary AI Components)
- **AI Agent**: Various agent types for different use cases
- **Basic LLM Chain**: Fundamental language model chains
- **Question and Answer Chain**: Q&A specific workflows
- **Summarization Chain**: Text summarization capabilities
- **Information Extractor**: Extract structured data from text
- **Text Classifier**: Classify text into categories
- **Sentiment Analysis**: Analyze emotional tone
- **LangChain Code**: Custom LangChain code execution
- **Vector Stores**: Multiple vector database integrations

#### Sub-nodes (Supporting Components)
- **Document Loaders**: Data ingestion from various sources
- **Embeddings**: Text-to-vector conversion services
- **Chat Models**: Conversational AI models
- **Memory Management**: Conversation state persistence
- **Tools**: External system integrations
- **Output Parsers**: Structure AI responses
- **Retrievers**: Document retrieval strategies

## B. AI Agents - Complete Documentation

### 1. Conversational Agent

#### Overview
Handles conversational interactions with memory and context management.

#### Configuration Parameters
```json
{
  "prompt": {
    "options": [
      "Take from previous node automatically",
      "Define below"
    ],
    "description": "Constructs the prompt from previous node's chatInput or custom definition"
  },
  "requireSpecificOutputFormat": {
    "type": "boolean",
    "description": "When enabled, connects output parsers",
    "parsers": [
      "Auto-fixing Output Parser",
      "Item List Output Parser", 
      "Structured Output Parser"
    ]
  }
}
```

#### Node Options
```json
{
  "humanMessage": "Refine agent behavior",
  "systemMessage": "Refine agent behavior", 
  "maxIterations": "Control execution cycles",
  "returnIntermediateSteps": "Include intermediate steps in output"
}
```

#### Template Example
```
TOOLS
------
Assistant can ask the user to use tools to look up information that may be helpful in answering the user's original question. The tools the human can use are:

{tools}

{format_instructions}

USER'S INPUT
--------------------
Here is the user's input (remember to respond with a markdown code snippet of a JSON blob with a single action, and NOTHING else):

{{input}}
```

### 2. ReAct Agent

#### Overview
Implements ReAct (Reasoning and Acting) logic, combining chain-of-thought prompting with action plan generation.

#### Key Features
- Chain-of-thought reasoning
- Action plan generation
- No memory support (cannot recall previous conversations)

#### Configuration
```json
{
  "prompt": {
    "takeFromPrevious": "Expects 'chatInput' from previous node",
    "defineBelow": "Provide static text or expression"
  },
  "requireSpecificOutputFormat": {
    "description": "Connect output parsers when enabled",
    "supportedParsers": [
      "Auto-fixing Output Parser",
      "Item List Output Parser",
      "Structured Output Parser"
    ]
  }
}
```

### 3. OpenAI Functions Agent

#### Overview
Uses OpenAI functions model to detect when functions should be called and respond with appropriate inputs.

#### Requirements
- OpenAI Chat Model (required)

#### Configuration Parameters
```json
{
  "prompt": {
    "takeFromPrevious": "Node expects 'chatInput' from previous node",
    "defineBelow": "Provide static text or expression in 'Prompt (User Message)'"
  },
  "requireSpecificOutputFormat": {
    "outputParsers": [
      "Auto-fixing Output Parser",
      "Item List Output Parser",
      "Structured Output Parser"
    ]
  }
}
```

#### Node Options
```json
{
  "systemMessage": {
    "description": "Message sent before conversation starts",
    "purpose": "Guide agent's decision-making"
  },
  "maxIterations": {
    "default": 10,
    "description": "Number of times model runs to generate good answer"
  },
  "returnIntermediateSteps": {
    "options": ["On", "Off"],
    "description": "Include intermediate steps in final output"
  }
}
```

### 4. Tools Agent

#### Overview
Uses various tools to perform actions and retrieve information, extending functionality beyond core LLM capabilities.

#### Supported Chat Models
- OpenAI Chat Model
- Groq Chat Model
- Mistral Cloud Chat Model
- Anthropic Chat Model
- Azure OpenAI Chat Model

#### Configuration
```json
{
  "prompt": {
    "takeFromPreviousNode": "Expects 'chatInput' from previous node",
    "defineBelow": "Provide static text or expression in 'Prompt (User Message)'"
  }
}
```

#### Available Tools (Extensive List)
```json
{
  "coreTools": [
    "Call n8n Workflow",
    "Code",
    "HTTP Request",
    "Calculator"
  ],
  "integrations": [
    "Action Network", "ActiveCampaign", "Affinity", "Agile CRM",
    "Airtable", "APITemplate.io", "Asana", "AWS Lambda", "AWS S3",
    "AWS SES", "AWS Textract", "AWS Transcribe", "Baserow",
    "Bubble", "Compression", "Crypto", "ClickUp", "CoinGecko",
    "DeepL", "DHL", "Discord", "Dropbox", "Elasticsearch",
    "ERPNext", "Facebook Graph API", "FileMaker", "Ghost",
    "Git", "GitHub", "GitLab", "Gmail", "Google Analytics",
    "Google BigQuery", "Google Calendar", "Google Chat",
    "Google Cloud Firestore", "Google Cloud Realtime Database",
    "Google Contacts", "Google Docs", "Google Drive",
    "Google Sheets", "Google Slides", "Google Tasks",
    "Google Translate", "Google Workspace Admin", "Gotify",
    "Grafana"
  ]
}
```

### 5. SQL Agent

#### Overview
Interacts with SQL databases to answer questions and execute queries.

#### Supported Databases
```json
{
  "mysql": {
    "credential": "MySQL credential (required)",
    "note": "Credential tunnel options not supported"
  },
  "sqlite": {
    "prerequisite": "Read/Write File From Disk node",
    "inputField": "SQLite file from previous node"
  },
  "postgres": {
    "credential": "Postgres credential (required)",
    "note": "Credential tunnel options not supported"
  }
}
```

#### Configuration
```json
{
  "prompt": {
    "takeFromPrevious": "Expects 'chatInput' from previous node",
    "defineBelow": "Static text or expression for 'Prompt (User Message)'"
  },
  "includeSampleRows": {
    "type": "integer",
    "default": 3,
    "description": "Number of sample rows in prompt (increases token usage)"
  }
}
```

### 6. Plan and Execute Agent

#### Overview
Executes plans based on defined steps and strategies.

#### Features
- Multi-step planning
- Sequential execution
- Plan adaptation based on results

## C. Vector Stores - Complete Documentation

### Overview
Vector stores enable semantic search and retrieval-augmented generation (RAG) by storing and querying text embeddings.

### Available Vector Store Integrations

#### 1. Simple Vector Store (In-Memory)
```json
{
  "type": "in-memory",
  "use_case": "Development and testing",
  "persistence": "Session-based only"
}
```

#### 2. Pinecone Vector Store
```json
{
  "type": "cloud-hosted",
  "features": ["Managed service", "High performance", "Scalability"],
  "use_case": "Production applications"
}
```

#### 3. Qdrant Vector Store
```json
{
  "type": "open-source",
  "features": ["Self-hosted option", "Cloud option", "High performance"],
  "use_case": "Flexible deployment"
}
```

#### 4. Supabase Vector Store
```json
{
  "type": "postgres-based",
  "features": ["Built on PostgreSQL", "Integrated with Supabase"],
  "use_case": "Full-stack applications"
}
```

#### 5. Zep Vector Store
```json
{
  "type": "conversational-memory",
  "features": ["Long-term memory", "Conversation context"],
  "use_case": "Chatbots and conversational AI"
}
```

#### 6. Milvus Vector Store
```json
{
  "type": "cloud-native",
  "features": ["Distributed", "High scalability", "Enterprise-grade"],
  "use_case": "Large-scale applications"
}
```

#### 7. MongoDB Atlas Vector Store
```json
{
  "type": "document-database",
  "features": ["Integrated with MongoDB", "Document + vector search"],
  "use_case": "MongoDB-based applications"
}
```

#### 8. PGVector Vector Store
```json
{
  "type": "postgres-extension",
  "features": ["PostgreSQL extension", "SQL compatibility"],
  "use_case": "PostgreSQL-based applications"
}
```

### Vector Store Operations

#### Insert Documents Operation
```javascript
// Workflow steps for inserting data
1. Add nodes to fetch source data
2. Insert Vector Store node with "Insert Documents" operation
3. Select embedding model for text-to-vector conversion
4. Add Default Data Loader for chunking:
   - Character Text Splitter: splits by character length
   - Recursive Character Text Splitter: splits by Markdown/HTML/code (recommended)
   - Token Text Splitter: splits by token count
5. (Optional) Add metadata to chunks for better filtering
```

#### Query Documents Operation
```javascript
// Workflow steps for querying data
1. Add agent to workflow
2. Add vector store as tool with description
3. Set limit for number of chunks to return
4. Enable "Include Metadata" for extra context
5. Add same embedding model used for insertion
```

### Zep Vector Store Detailed Configuration

#### Operation Modes
```json
{
  "getManyMode": {
    "collectionName": "string (required)",
    "prompt": "string (required) - search query",
    "limit": "integer (optional) - e.g., 10"
  },
  "insertDocuments": {
    "collectionName": "string (required)",
    "documents": "array of documents to insert"
  },
  "retrieveForChain": {
    "collectionName": "string (required)",
    "description": "Used by retriever chains"
  },
  "retrieveForAgent": {
    "name": "string (required) - vector store name",
    "description": "string (required) - explain to LLM what this tool does",
    "collectionName": "string (required)",
    "limit": "integer (optional)"
  }
}
```

#### Node Options
```json
{
  "embeddingDimensions": {
    "description": "Size of float array for semantic meaning",
    "note": "Must be consistent for embedding and querying"
  },
  "isAutoEmbedded": {
    "default": true,
    "description": "Disable to configure embeddings in Zep instead of n8n"
  },
  "rerankResults": {
    "description": "Enable reranking of results",
    "requirement": "Connect reranking node"
  }
}
```

## D. Embeddings Models

### Overview
Embedding models convert text into vector representations for semantic search and similarity matching.

### Available Embedding Providers

#### 1. OpenAI Embeddings
```json
{
  "provider": "OpenAI",
  "models": ["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"],
  "use_case": "General purpose, high quality"
}
```

#### 2. Google Embeddings
```json
{
  "providers": {
    "google_gemini": "Google Gemini embeddings",
    "google_palm": "Google PaLM embeddings", 
    "google_vertex": "Google Vertex AI embeddings"
  }
}
```

#### 3. Azure OpenAI Embeddings
```json
{
  "provider": "Azure OpenAI",
  "description": "OpenAI models hosted on Azure",
  "use_case": "Enterprise Azure deployments"
}
```

#### 4. AWS Bedrock Embeddings
```json
{
  "provider": "AWS Bedrock",
  "description": "Amazon's managed AI service",
  "use_case": "AWS ecosystem integration"
}
```

#### 5. Cohere Embeddings
```json
{
  "provider": "Cohere",
  "description": "Cohere's embedding models",
  "use_case": "Multilingual and specialized embeddings"
}
```

#### 6. Mistral Cloud Embeddings
```json
{
  "provider": "Mistral",
  "description": "Mistral AI's embedding models",
  "use_case": "European AI alternative"
}
```

#### 7. HuggingFace Inference Embeddings
```json
{
  "provider": "HuggingFace",
  "description": "Access to HuggingFace Hub models",
  "use_case": "Open source and custom models"
}
```

#### 8. Ollama Embeddings
```json
{
  "provider": "Ollama",
  "description": "Local model hosting",
  "use_case": "Privacy-focused local deployment"
}
```

## E. Chat Models

### Overview
Chat models power conversational AI interactions in n8n workflows.

### Available Chat Model Providers

#### 1. OpenAI Chat Model
```json
{
  "provider": "OpenAI",
  "models": ["gpt-3.5-turbo", "gpt-4", "gpt-4-turbo"],
  "features": ["Function calling", "JSON mode", "Vision"],
  "common_issues": "See documentation for troubleshooting"
}
```

#### 2. Anthropic Chat Model
```json
{
  "provider": "Anthropic",
  "models": ["claude-3-sonnet", "claude-3-opus", "claude-3-haiku"],
  "features": ["Large context window", "Constitutional AI"]
}
```

#### 3. Google Chat Models
```json
{
  "google_gemini": {
    "models": ["gemini-pro", "gemini-pro-vision"],
    "features": ["Multimodal", "Large context"]
  },
  "google_vertex": {
    "description": "Google Cloud managed AI",
    "use_case": "Enterprise Google Cloud deployments"
  }
}
```

#### 4. Azure OpenAI Chat Model
```json
{
  "provider": "Azure OpenAI",
  "description": "OpenAI models on Azure infrastructure",
  "use_case": "Enterprise Azure integration"
}
```

#### 5. AWS Bedrock Chat Model
```json
{
  "provider": "AWS Bedrock",
  "description": "Amazon's managed AI models",
  "models": ["Claude", "Llama", "Titan"]
}
```

#### 6. Groq Chat Model
```json
{
  "provider": "Groq",
  "description": "Ultra-fast inference",
  "use_case": "High-speed AI responses"
}
```

#### 7. Mistral Cloud Chat Model
```json
{
  "provider": "Mistral",
  "models": ["mistral-small", "mistral-medium", "mistral-large"],
  "use_case": "European AI alternative"
}
```

#### 8. Ollama Chat Model
```json
{
  "provider": "Ollama",
  "description": "Local model hosting",
  "models": ["llama2", "codellama", "mistral"],
  "use_case": "Privacy-focused local deployment",
  "common_issues": "See documentation for local setup"
}
```

#### 9. Other Providers
```json
{
  "deepseek": "DeepSeek Chat Model",
  "openrouter": "OpenRouter Chat Model - Access to multiple providers",
  "xai_grok": "xAI Grok Chat Model",
  "cohere": "Cohere Model - Command and other models"
}
```

## F. Memory Management

### Overview
Memory management enables AI agents to maintain conversation context and state across interactions.

### Available Memory Types

#### 1. Chat Memory Manager
```json
{
  "type": "general",
  "description": "General-purpose conversation memory",
  "use_case": "Basic chat applications"
}
```

#### 2. Simple Memory
```json
{
  "type": "in-memory",
  "description": "Session-based memory storage",
  "persistence": "Temporary"
}
```

#### 3. Database-backed Memory
```json
{
  "mongodb_memory": "MongoDB Chat Memory",
  "redis_memory": "Redis Chat Memory", 
  "postgres_memory": "Postgres Chat Memory",
  "use_case": "Persistent conversation history"
}
```

#### 4. Specialized Memory
```json
{
  "motorhead": "Motorhead memory service",
  "xata": "Xata database memory",
  "zep": "Zep long-term memory service"
}
```

## G. Tools and Retrievers

### LangChain Tools
```json
{
  "calculator": "Mathematical calculations",
  "custom_code": "Execute custom code snippets",
  "mcp_client": "Model Context Protocol client",
  "searxng": "SearXNG search integration",
  "serpapi": "Google Search via SerpApi",
  "think_tool": "Reasoning and thinking operations",
  "vector_store_qa": "Question answering from vector stores",
  "wikipedia": "Wikipedia information retrieval",
  "wolfram_alpha": "Wolfram Alpha computations",
  "call_workflow": "Execute other n8n workflows"
}
```

### Retrievers
```json
{
  "contextual_compression": "Compress retrieved context",
  "multiquery": "Multiple query strategies",
  "vector_store": "Retrieve from vector stores",
  "workflow": "Retrieve from n8n workflows"
}
```

## H. Output Parsers

### Overview
Output parsers structure and validate AI model responses into usable formats.

### Available Parsers

#### 1. Auto-fixing Output Parser
```json
{
  "description": "Automatically fixes malformed output",
  "use_case": "Robust JSON parsing with error correction"
}
```

#### 2. Item List Output Parser
```json
{
  "description": "Parses responses into structured lists",
  "use_case": "Extract multiple items from responses"
}
```

#### 3. Structured Output Parser
```json
{
  "description": "Enforces specific output schemas",
  "use_case": "Ensure consistent data structure",
  "common_issues": "See documentation for schema definition"
}
```

## I. Advanced Features

### AI Tool Node Output Control
```json
{
  "output_parameter": {
    "simplified": "Returns max 10 fields, similar to 'Simplify'",
    "raw": "Returns all available fields",
    "selected_fields": "Multi-option selector for specific fields"
  },
  "purpose": "Prevent context window issues for AI agents"
}
```

### Reranking
```json
{
  "cohere_reranker": {
    "description": "Improve search result relevance",
    "use_case": "Enhanced vector search quality"
  }
}
```

### Model Selection
```json
{
  "model_selector": {
    "description": "Dynamic model selection based on context",
    "use_case": "Optimize performance and cost"
  }
}
```

## J. RAG (Retrieval-Augmented Generation) Implementation

### Complete RAG Workflow

#### 1. Data Ingestion Phase
```javascript
// Step 1: Fetch source data
const sourceNodes = [
  "HTTP Request", // API data
  "Google Sheets", // Spreadsheet data  
  "File Reader", // Local files
  "GitHub Loader" // Repository data
];

// Step 2: Process and chunk data
const dataLoader = {
  "node": "Default Data Loader",
  "chunking_strategies": {
    "character_splitter": "Split by character length",
    "recursive_splitter": "Split by Markdown/HTML/code (recommended)",
    "token_splitter": "Split by token count"
  }
};

// Step 3: Generate embeddings
const embeddingModel = [
  "OpenAI Embeddings",
  "Google Gemini Embeddings", 
  "Cohere Embeddings"
];

// Step 4: Store in vector database
const vectorStore = {
  "operation": "Insert Documents",
  "metadata": "Optional enrichment for better filtering"
};
```

#### 2. Query Phase
```javascript
// Step 1: Configure agent
const agent = {
  "type": "Tools Agent",
  "chat_model": "OpenAI Chat Model"
};

// Step 2: Add vector store as tool
const vectorTool = {
  "vector_store": "Same store used for insertion",
  "description": "Help agent understand when to use it",
  "limit": "Number of chunks to return",
  "include_metadata": true
};

// Step 3: Use same embedding model
const queryEmbedding = "Same model as insertion phase";
```

## K. Common Issues and Troubleshooting

### AI Agent Error: Invalid Content Value
```javascript
// Error: 400 Invalid value for 'content': expected a string, got null
// Cause: Prompt input receives null instead of string
// Solution: Check chat trigger data and expressions for null values

const troubleshooting = {
  "check_inputs": "Verify all required inputs are present",
  "validate_expressions": "Ensure expressions resolve to strings",
  "test_connections": "Verify node connections are correct"
};
```

### Vector Store Common Issues
```javascript
const vectorStoreIssues = {
  "embedding_consistency": "Use same embedding model for insert and query",
  "chunking_strategy": "Optimize chunk size for your use case",
  "metadata_filtering": "Ensure metadata format consistency",
  "memory_management": "Monitor memory usage with large datasets"
};
```

### Chat Model Issues
```javascript
const chatModelIssues = {
  "token_limits": "Monitor context window usage",
  "rate_limiting": "Implement proper retry logic",
  "api_keys": "Verify credentials are correctly configured",
  "model_availability": "Check model availability in your region"
};
```

## L. Best Practices

### Agent Design
```javascript
const agentBestPractices = {
  "prompt_engineering": "Write clear, specific prompts",
  "tool_selection": "Choose appropriate tools for the task",
  "error_handling": "Implement robust error handling",
  "iteration_limits": "Set reasonable max iterations",
  "memory_management": "Use appropriate memory type for use case"
};
```

### Vector Store Optimization
```javascript
const vectorStoreBestPractices = {
  "chunking": "Optimize chunk size (typically 200-1000 tokens)",
  "metadata": "Add meaningful metadata for filtering",
  "embedding_quality": "Choose high-quality embedding models",
  "indexing": "Use appropriate vector database for scale",
  "retrieval": "Fine-tune retrieval parameters"
};
```

### Performance Optimization
```javascript
const performanceTips = {
  "caching": "Cache frequently accessed embeddings",
  "batch_processing": "Process data in batches",
  "model_selection": "Choose appropriate model size for task",
  "resource_monitoring": "Monitor token usage and costs",
  "error_recovery": "Implement graceful degradation"
};
```


===============================================================================
3. TUTORIAL AND FIRST WORKFLOW GUIDES
===============================================================================

## A. Creating Your First n8n Workflow

### Overview
n8n workflows are composed of nodes connected together to automate tasks. Each workflow starts with a trigger and flows through various action nodes to complete tasks.

### Basic Workflow Structure
```json
{
  "meta": {
    "templateCredsSetupCompleted": true,
    "instanceId": "unique-instance-id"
  },
  "nodes": [
    {
      "parameters": {},
      "name": "Start Node",
      "type": "n8n-nodes-base.start",
      "typeVersion": 1,
      "position": [100, 200]
    }
  ],
  "connections": {
    "Start Node": {
      "main": [
        [
          {
            "node": "Next Node",
            "type": "main",
            "index": 0
          }
        ]
      ]
    }
  }
}
```

### Manual Trigger Configuration
```json
{
  "id": "30-e56cb62b7b5c",
  "name": "When clicking \"Execute workflow\"",
  "type": "n8n-nodes-base.manualTrigger",
  "typeVersion": 1,
  "position": [780, 600]
}
```

## B. Common Workflow Patterns

### 1. Data Processing Workflow
```json
{
  "pattern": "Extract -> Transform -> Load",
  "nodes": [
    "HTTP Request (Extract data)",
    "Code Node (Transform data)",
    "Database Node (Load data)"
  ],
  "example": "API data processing pipeline"
}
```

### 2. Conditional Logic Workflow
```json
{
  "pattern": "Trigger -> Condition -> Branch",
  "nodes": [
    "Trigger Node",
    "IF Node",
    "Different actions based on condition"
  ],
  "example": "Order processing with different paths for different order types"
}
```

### 3. Error Handling Workflow
```json
{
  "pattern": "Main Workflow -> Error Trigger -> Notification",
  "workflow_example": {
    "nodes": [
      {
        "parameters": {},
        "name": "Error Trigger",
        "type": "n8n-nodes-base.errorTrigger",
        "typeVersion": 1,
        "position": [720, -380]
      },
      {
        "parameters": {
          "channel": "channelname",
          "text": "This workflow {{$node[\"Error Trigger\"].json[\"workflow\"][\"name\"]}} failed.\nHave a look at it here: {{$node[\"Error Trigger\"].json[\"execution\"][\"url\"]}}",
          "attachments": [],
          "otherOptions": {}
        },
        "name": "Slack",
        "type": "n8n-nodes-base.slack",
        "position": [900, -380],
        "typeVersion": 1,
        "credentials": {
          "slackApi": {
            "id": "17",
            "name": "slack_credentials"
          }
        }
      }
    ],
    "connections": {
      "Error Trigger": {
        "main": [
          [
            {
              "node": "Slack",
              "type": "main",
              "index": 0
            }
          ]
        ]
      }
    }
  }
}
```

### 4. Data Processing and Notification Workflow
```json
{
  "description": "Complex workflow combining multiple data sources",
  "workflow_structure": {
    "connections": {
      "When clicking \"Execute workflow\"": {
        "main": [
          [
            {
              "node": "HTTP Request",
              "type": "main",
              "index": 0
            },
            {
              "node": "Airtable",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "HTTP Request": {
        "main": [
          [
            {
              "node": "Merge",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Airtable": {
        "main": [
          [
            {
              "node": "Merge",
              "type": "main",
              "index": 1
            }
          ]
        ]
      },
      "Merge": {
        "main": [
          [
            {
              "node": "Sort",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Sort": {
        "main": [
          [
            {
              "node": "Loop Over Items",
              "type": "main",
              "index": 0
            },
            {
              "node": "If",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Loop Over Items": {
        "main": [
          null,
          [
            {
              "node": "Edit Fields",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Edit Fields": {
        "main": [
          [
            {
              "node": "Date & Time",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Date & Time": {
        "main": [
          [
            {
              "node": "Convert to File1",
              "type": "main",
              "index": 0
            }
          ]
        ]
      },
      "Convert to File1": {
        "main": [
          [
            {
              "node": "Discord1",
              "type": "main",
              "index": 0
            }
          ]
        ]
      }
    }
  }
}
```

## C. n8n Expression Language for Beginners

### Basic Expression Syntax
```javascript
// Access JSON data from previous node
{{ $json.fieldName }}

// Access specific node output
{{ $node["Node Name"].json.fieldName }}

// Access multiple items
{{ $json.items[0].fieldName }}

// Conditional expressions
{{ $json.status === 'active' ? 'Enabled' : 'Disabled' }}

// String manipulation
{{ $json.firstName + ' ' + $json.lastName }}

// Date formatting
{{ $json.createdAt.toDate().toFormat('yyyy-MM-dd') }}
```

### Customer Messenger Example
```javascript
// Dynamic message generation
"Hi {{ $json.customer_name }}. Your description is: {{ $json.customer_description }}"

// Conditional file naming
"{{ $runIndex > 0 ? 'file_low_orders' : 'file_high_orders' }}"

// Complex data transformation
"{{ $json.region }} - {{ $json.customerSince }} - {{ $json.orderPrice }}"
```

### Workflow Metadata Access
```javascript
// Workflow information
$workflow.name           // Get workflow name
$workflow.id            // Get workflow ID

// Execution context
$execution.id           // Get execution ID
$execution.mode         // Get execution mode (test/production)
$execution.resumeUrl    // Get resume URL for Wait nodes

// Node execution context
$runIndex              // How many times current node has been executed (0-based)
$nodeVersion          // Get current node version

// Static data access
$getWorkflowStaticData(type)  // Access workflow static data
```

## D. Workflow Management and Templates

### Workflow Templates Structure
```json
{
  "workflow_item_schema": {
    "id": "number",
    "name": "string",
    "totalViews": "number",
    "recentViews": "number",
    "createdAt": "string",
    "user": {
      "username": "string",
      "verified": "boolean"
    },
    "nodes": [
      {
        "id": "number",
        "icon": "string",
        "name": "string",
        "group": "string",
        "defaults": {
          "name": "string",
          "color": "string"
        },
        "codex": {
          "data": {
            "details": "string",
            "resources": {
              "generic": [
                {
                  "url": "string",
                  "icon": "string",
                  "label": "string"
                }
              ],
              "primaryDocumentation": [
                {
                  "url": "string"
                }
              ]
            },
            "categories": ["string"],
            "nodeVersion": "string",
            "codexVersion": "string"
          }
        }
      }
    ]
  }
}
```

### Collection Schema
```json
{
  "collection_item_schema": {
    "id": "number",
    "rank": "number", 
    "name": "string",
    "totalViews": "number",
    "createdAt": "string",
    "workflows": [
      {
        "id": "number"
      }
    ],
    "nodes": []
  }
}
```

### Custom Workflow Templates Configuration
```bash
# Set custom templates host
N8N_TEMPLATES_HOST=<your_api_base_url>

# Disable default templates
N8N_TEMPLATES_ENABLED=false
```

### Required Template API Endpoints
```
GET /templates/workflows/<id>
GET /templates/search  
GET /templates/collections/<id>
GET /templates/collections
GET /templates/categories
GET /health
```

## E. CLI Commands for Workflow Management

### Workflow Export Commands
```bash
# Export all workflows to stdout
n8n export:workflow --all

# Export specific workflow by ID to file
n8n export:workflow --id=<ID> --output=file.json

# Export all workflows to directory
n8n export:workflow --all --output=backups/latest/file.json

# Create backup with proper structure
n8n export:workflow --backup --output=backups/latest/
```

### Workflow Import Commands
```bash
# Import workflows from directory
n8n import:workflow --separate --input=backups/latest/

# Import single workflow file
n8n import:workflow --input=workflow.json
```

### Workflow Execution Commands
```bash
# Execute workflow by ID
n8n execute --id <ID>

# Start n8n instance
n8n start

# Start n8n with development server  
n8n start --tunnel
```

## F. REST API for Workflow Management

### Create Workflow
```bash
# Create new workflow via REST API
POST https://<n8n-domain>/rest/workflows/

# Request body: Complete workflow definition JSON
# Response: Returns workflow ID
```

### Get Workflows with Pagination
```bash
# Get first page of active workflows (self-hosted)
curl -X 'GET' \
  '<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version>/workflows?active=true&limit=150' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'

# Get workflows from n8n Cloud
curl -X 'GET' \
  '<your-cloud-instance>/api/v<version>/workflows?active=true&limit=150' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: <your-api-key>'
```

### Workflow API Response Example
```json
{
  "data": {
    "id": "1012",
    "name": "Nathan's Workflow",
    "active": false,
    "nodes": [
      {
        "parameters": {},
        "name": "Start",
        "type": "n8n-nodes-base.start",
        "typeVersion": 1,
        "position": [130, 640]
      },
      {
        "parameters": {
          "authentication": "headerAuth",
          "url": "https://internal.users.n8n.cloud/webhook/custom-erp",
          "options": {
            "splitIntoItems": true
          },
          "headerParametersUi": {
            "parameter": [
              {
                "name": "unique_id",
                "value": "recLhLYQbzNSFtHNq"
              }
            ]
          }
        },
        "name": "HTTP Request",
        "type": "n8n-nodes-base.httpRequest",
        "typeVersion": 1,
        "position": [430, 300],
        "credentials": {
          "httpHeaderAuth": "beginner_course"
        }
      }
    ],
    "connections": {
      "Start": {
        "main": [
          [
            {
              "node": "HTTP Request",
              "type": "main",
              "index": 0
            }
          ]
        ]
      }
    }
  }
}
```

## G. Advanced Workflow Patterns

### 1. Most Recent Google Drive File Retrieval
```javascript
// Workflow steps to get most recent file
const workflow_steps = [
  "1. Add Google Drive node with File/Folder resource and Search operation",
  "2. Enable 'Return All' to sort through all files", 
  "3. Set 'What to Search' filter to 'Files'",
  "4. In Options, set Fields to 'All'",
  "5. Connect Sort node to Google Drive output",
  "6. Choose 'Simple' sort type with 'modifiedTime' field",
  "7. Choose 'Descending' sort order",
  "8. Add Limit node with Max Items set to 1",
  "9. Connect another Google Drive node to get file by ID",
  "10. Use expression {{ $json.id }} for file ID"
];
```

### 2. Sales Data Processing Workflow
```json
{
  "workflow_description": "Process sales data from multiple sources",
  "nodes": [
    {
      "name": "Manual Trigger",
      "type": "n8n-nodes-base.manualTrigger",
      "purpose": "Start workflow manually"
    },
    {
      "name": "HTTP Request", 
      "type": "n8n-nodes-base.httpRequest",
      "purpose": "Fetch external sales data"
    },
    {
      "name": "Airtable",
      "type": "n8n-nodes-base.airtable", 
      "purpose": "Get internal sales records"
    },
    {
      "name": "Merge",
      "type": "n8n-nodes-base.merge",
      "purpose": "Combine data from both sources"
    },
    {
      "name": "Sort",
      "type": "n8n-nodes-base.sort",
      "purpose": "Sort combined data"
    },
    {
      "name": "IF",
      "type": "n8n-nodes-base.if",
      "purpose": "Conditional processing"
    },
    {
      "name": "Loop Over Items",
      "type": "n8n-nodes-base.splitInBatches",
      "purpose": "Process items individually"
    },
    {
      "name": "Edit Fields",
      "type": "n8n-nodes-base.set",
      "purpose": "Transform data structure"
    },
    {
      "name": "Date & Time",
      "type": "n8n-nodes-base.dateTime",
      "purpose": "Format dates"
    },
    {
      "name": "Convert to File",
      "type": "n8n-nodes-base.convertToFile",
      "purpose": "Create file output"
    },
    {
      "name": "Gmail",
      "type": "n8n-nodes-base.gmail",
      "purpose": "Send results via email"
    },
    {
      "name": "Discord",
      "type": "n8n-nodes-base.discord",
      "purpose": "Send notifications"
    }
  ]
}
```

### 3. Email Processing with Attachments
```json
{
  "gmail_node_config": {
    "parameters": {
      "sendTo": "<EMAIL>",
      "subject": "Your TPS Reports",
      "emailType": "text",
      "message": "Please find your TPS report attached.",
      "options": {
        "attachmentsUi": {
          "attachmentsBinary": [{}]
        }
      }
    },
    "credentials": {
      "gmailOAuth2": {
        "id": "credential-id",
        "name": "Gmail account"
      }
    }
  }
}
```

## H. Error Handling Best Practices

### Default Error Data Structure
```json
{
  "error_trigger_data": [
    {
      "execution": {
        "id": "231",
        "url": "https://n8n.example.com/execution/231",
        "retryOf": "34",
        "error": {
          "message": "Example Error Message",
          "stack": "Stacktrace"
        },
        "lastNodeExecuted": "Node With Error",
        "mode": "manual"
      },
      "workflow": {
        "id": "1",
        "name": "Example Workflow"
      }
    }
  ]
}
```

### Common Error Messages
```javascript
// Unexecuted node reference error
const errorMessage = "An expression references the node '<node-name>', but it hasn't been executed yet. Either change the expression, or re-wire your workflow to make sure that node executes first.";

// Solutions:
const solutions = [
  "Check node execution order",
  "Verify node connections",
  "Ensure referenced nodes execute before current node",
  "Use conditional expressions to handle missing data"
];
```

## I. Execution Timeout Configuration

### Global Soft Timeout
```bash
# Set global workflow timeout (in seconds)
export EXECUTIONS_TIMEOUT=3600

# Disable timeout (-1 value)
export EXECUTIONS_TIMEOUT=-1
```

### Individual Workflow Hard Timeout
```bash
# Set maximum execution time per workflow
export EXECUTIONS_TIMEOUT_MAX=7200
```

## J. Keyboard Shortcuts for Workflow Development

### Essential Shortcuts
```javascript
const shortcuts = {
  "Ctrl + Alt + n": "Create new workflow",
  "Ctrl + o": "Open workflow",
  "Ctrl + s": "Save current workflow", 
  "Ctrl + z": "Undo",
  "Ctrl + Shift + z": "Redo",
  "Ctrl + Enter": "Execute workflow"
};
```

## K. White-labeling and Customization

### Custom HTML Title
```html
<!DOCTYPE html>
<html lang="en">
<head>
  <!-- Replace html title attribute -->
  <title>My Brand - Workflow Automation</title>
</head>
```

### Repository Setup for Custom Branding
```bash
# Clone your forked n8n repository
git clone https://github.com/<your-organization>/n8n.git n8n
cd n8n

# Install dependencies and build
npm install
npm run build
npm run start
```

## L. API Version Updates (v1.53.0)

### New Capabilities
```json
{
  "variable_management": {
    "create_variables": "POST /variables",
    "read_variables": "GET /variables",
    "delete_variables": "DELETE /variables/:id"
  },
  "workflow_management": {
    "filter_by_project": "GET /workflows?project=<project-id>",
    "transfer_workflows": "POST /workflows/:id/transfer"
  }
}
```

## M. Best Practices for Beginners

### 1. Workflow Design Principles
```javascript
const bestPractices = {
  "start_simple": "Begin with basic workflows before adding complexity",
  "test_frequently": "Use manual triggers to test workflow steps",
  "error_handling": "Always include error handling workflows",
  "documentation": "Use descriptive node names and add notes",
  "modular_design": "Break complex workflows into smaller, reusable pieces"
};
```

### 2. Node Configuration Tips
```javascript
const nodeTips = {
  "expressions": "Start with simple expressions before complex logic",
  "credentials": "Set up credentials before configuring nodes",
  "testing": "Test individual nodes before connecting them",
  "data_flow": "Understand data structure between nodes",
  "error_messages": "Read error messages carefully for debugging"
};
```

### 3. Common Beginner Mistakes
```javascript
const commonMistakes = {
  "unexecuted_references": "Referencing nodes that haven't executed yet",
  "missing_credentials": "Forgetting to configure node credentials",
  "wrong_data_types": "Mismatching data types in expressions",
  "complex_first_workflows": "Starting with overly complex workflows",
  "no_error_handling": "Not planning for error scenarios"
};
```

### 4. Recommended Learning Path
```javascript
const learningPath = [
  "1. Start with manual trigger and HTTP Request node",
  "2. Learn basic expressions with Edit Fields/Set node",
  "3. Practice with IF node for conditional logic",
  "4. Try Loop Over Items for batch processing",
  "5. Add error handling with Error Trigger",
  "6. Explore integrations with common services",
  "7. Build complex multi-step workflows",
  "8. Learn advanced features like AI agents and vector stores"
];
```


===============================================================================
4. HOSTING AND CONFIGURATION GUIDES
===============================================================================

## A. Environment Variables - Complete Reference

### Deployment Variables
```bash
# Core Configuration
N8N_EDITOR_BASE_URL=""                    # Public URL for editor access and emails
N8N_CONFIG_FILES=""                       # Path to JSON configuration files
N8N_DISABLE_UI=false                      # Disable the UI (true/false)
N8N_PREVIEW_MODE=false                    # Run in preview mode
N8N_TEMPLATES_ENABLED=false               # Enable workflow templates
N8N_TEMPLATES_HOST="https://api.n8n.io"  # Custom templates API host
N8N_ENCRYPTION_KEY=""                     # Custom encryption key for credentials
N8N_USER_FOLDER="user-folder"             # Path for .n8n folder creation

# Network Configuration
N8N_PATH="/"                              # Deployment path
N8N_HOST="localhost"                      # Host name
N8N_PORT=5678                             # HTTP port
N8N_LISTEN_ADDRESS="0.0.0.0"              # IP address to listen on
N8N_PROTOCOL="http"                       # Protocol (http/https)
N8N_SSL_KEY=""                            # SSL key for HTTPS
N8N_SSL_CERT=""                           # SSL certificate for HTTPS

# Proxy and Tunnel Configuration
N8N_TUNNEL_SUBDOMAIN=""                   # Tunnel subdomain (random if not set)
N8N_PROXY_HOPS=0                          # Number of reverse-proxies behind

# UI and UX Configuration
N8N_PERSONALIZATION_ENABLED=true         # Ask personalization questions
N8N_VERSION_NOTIFICATIONS_ENABLED=true   # Show version notifications
N8N_VERSION_NOTIFICATIONS_ENDPOINT="https://api.n8n.io/versions/"
N8N_VERSION_NOTIFICATIONS_INFO_URL="https://docs.n8n.io/getting-started/installation/updating.html"
N8N_HIRING_BANNER_ENABLED=true            # Show hiring banner in console

# Telemetry and Diagnostics
N8N_DIAGNOSTICS_ENABLED=true              # Share anonymous telemetry
N8N_DIAGNOSTICS_CONFIG_FRONTEND="1zPn9bgWPzlQc0p8Gj1uiK6DOTn;https://telemetry.n8n.io"
N8N_DIAGNOSTICS_CONFIG_BACKEND="1zPn7YoGC3ZXE9zLeTKLuQCB4F6;https://telemetry.n8n.io/v1/batch"

# Backend Communication
N8N_PUSH_BACKEND="websocket"              # Use SSE or WebSockets for UI updates
VUE_APP_URL_BASE_API="http://localhost:5678/"  # Frontend to backend API URL

# API Configuration
N8N_PUBLIC_API_SWAGGERUI_DISABLED=false   # Disable Swagger UI
N8N_PUBLIC_API_DISABLED=false             # Disable public API
N8N_PUBLIC_API_ENDPOINT="api"             # Public API path

# System Configuration
N8N_GRACEFUL_SHUTDOWN_TIMEOUT=30          # Shutdown timeout (seconds)
N8N_DEV_RELOAD=false                      # Auto-reload on source changes
N8N_REINSTALL_MISSING_PACKAGES=false      # Auto-reinstall missing packages
```

### Database Configuration Variables (PostgreSQL)
```bash
# PostgreSQL Connection
DB_TYPE="postgresdb"
DB_POSTGRESDB_DATABASE="n8n"              # Database name
DB_POSTGRESDB_HOST="localhost"            # PostgreSQL host
DB_POSTGRESDB_PORT=5432                   # PostgreSQL port
DB_POSTGRESDB_USER="postgres"             # PostgreSQL user
DB_POSTGRESDB_PASSWORD=""                 # PostgreSQL password
DB_POSTGRESDB_SCHEMA="public"             # PostgreSQL schema

# Connection Pool Configuration
DB_POSTGRESDB_POOL_SIZE=2                 # Parallel connections
DB_POSTGRESDB_CONNECTION_TIMEOUT=20000    # Connection timeout (ms)
DB_POSTGRESDB_IDLE_CONNECTION_TIMEOUT=30000  # Idle connection timeout

# SSL Configuration
DB_POSTGRESDB_SSL_ENABLED=false           # Enable SSL
DB_POSTGRESDB_SSL_CA=""                   # SSL certificate authority
DB_POSTGRESDB_SSL_CERT=""                 # SSL certificate
DB_POSTGRESDB_SSL_KEY=""                  # SSL key
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=true  # Reject unauthorized SSL
```

### Queue Mode Configuration (Redis/BullMQ)
```bash
# Redis Connection
QUEUE_BULL_PREFIX=""                      # Queue keys prefix
QUEUE_BULL_REDIS_DB=0                     # Redis database number
QUEUE_BULL_REDIS_HOST="localhost"         # Redis host
QUEUE_BULL_REDIS_PORT=6379                # Redis port
QUEUE_BULL_REDIS_USERNAME=""              # Redis username (v6+)
QUEUE_BULL_REDIS_PASSWORD=""              # Redis password
QUEUE_BULL_REDIS_TIMEOUT_THRESHOLD=10000  # Redis timeout (ms)

# Redis Cluster Configuration
QUEUE_BULL_REDIS_CLUSTER_NODES=""         # Comma-separated cluster nodes
QUEUE_BULL_REDIS_TLS=false                # Enable TLS
QUEUE_BULL_REDIS_DUALSTACK=false          # Enable IPv4/IPv6 support

# Queue Worker Configuration
QUEUE_HEALTH_CHECK_ACTIVE=false           # Enable health checks
QUEUE_HEALTH_CHECK_PORT=""                # Health check port
QUEUE_WORKER_LOCK_DURATION=30000          # Worker lease period (ms)
QUEUE_WORKER_LOCK_RENEW_TIME=15000        # Lease renewal frequency (ms)
QUEUE_WORKER_STALLED_INTERVAL=30000       # Stalled job check interval
QUEUE_WORKER_MAX_STALLED_COUNT=1          # Max stalled job reprocessing
```

### Security Configuration Variables
```bash
# File and Environment Access
N8N_BLOCK_ENV_ACCESS_IN_NODE=false        # Block environment variable access
N8N_BLOCK_FILE_ACCESS_TO_N8N_FILES=true   # Block access to .n8n files
N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=false  # Set 0600 permissions on settings
N8N_RESTRICT_FILE_ACCESS_TO=""            # Limit file access to directories

# Security Audit
N8N_SECURITY_AUDIT_DAYS_ABANDONED_WORKFLOW=90  # Days to consider workflow abandoned

# Cookie Security
N8N_SECURE_COOKIE=true                    # HTTPS-only cookies
N8N_SAMESITE_COOKIE="lax"                 # Cross-site cookie behavior (strict/lax/none)
```

### Endpoint Configuration Variables
```bash
# Payload and File Size Limits
N8N_PAYLOAD_SIZE_MAX=16                   # Max payload size (MiB)
N8N_FORMDATA_FILE_SIZE_MAX=200            # Max form-data file size (MiB)

# Metrics Configuration
N8N_METRICS=false                         # Enable /metrics endpoint
N8N_METRICS_PREFIX="n8n_"                 # Metrics prefix
N8N_METRICS_INCLUDE_DEFAULT_METRICS=true  # Include system metrics
N8N_METRICS_INCLUDE_CACHE_METRICS=false   # Include cache metrics
N8N_METRICS_INCLUDE_MESSAGE_EVENT_BUS_METRICS=false  # Include event metrics
N8N_METRICS_INCLUDE_WORKFLOW_ID_LABEL=false  # Include workflow ID in metrics
N8N_METRICS_INCLUDE_NODE_TYPE_LABEL=false # Include node type in metrics
N8N_METRICS_INCLUDE_CREDENTIAL_TYPE_LABEL=false  # Include credential type
N8N_METRICS_INCLUDE_API_ENDPOINTS=false   # Include API endpoint metrics
N8N_METRICS_INCLUDE_API_PATH_LABEL=false  # Include API path in metrics
N8N_METRICS_INCLUDE_API_METHOD_LABEL=false  # Include HTTP method in metrics
N8N_METRICS_INCLUDE_API_STATUS_CODE_LABEL=false  # Include status code
N8N_METRICS_INCLUDE_QUEUE_METRICS=false   # Include queue metrics
N8N_METRICS_QUEUE_METRICS_INTERVAL=20     # Queue metrics update interval

# Endpoint Paths
N8N_ENDPOINT_REST="rest"                  # REST endpoint path
N8N_ENDPOINT_WEBHOOK="webhook"            # Webhook endpoint path
N8N_ENDPOINT_WEBHOOK_TEST="webhook-test"  # Test webhook path
N8N_ENDPOINT_WEBHOOK_WAIT="webhook-waiting"  # Waiting webhook path
WEBHOOK_URL=""                            # Manual webhook URL override

# Production Configuration
N8N_DISABLE_PRODUCTION_MAIN_PROCESS=false  # Disable production webhooks from main process
```

### Task Runner Configuration Variables
```bash
# Task Runner Instance Configuration
N8N_RUNNERS_ENABLED=false                 # Enable task runners
N8N_RUNNERS_MODE="internal"               # Launch mode (internal/external)
N8N_RUNNERS_AUTH_TOKEN=""                 # Authentication token (external mode)
N8N_RUNNERS_BROKER_PORT=5679              # Task broker port
N8N_RUNNERS_BROKER_LISTEN_ADDRESS="127.0.0.1"  # Broker listen address
N8N_RUNNERS_MAX_PAYLOAD=1073741824        # Max payload size (bytes)
N8N_RUNNERS_MAX_OLD_SPACE_SIZE=""         # Node.js max old space size
N8N_RUNNERS_MAX_CONCURRENCY=5             # Concurrent tasks per runner
N8N_RUNNERS_TASK_TIMEOUT=60               # Task timeout (seconds)
N8N_RUNNERS_HEARTBEAT_INTERVAL=30         # Heartbeat interval (seconds)

# Task Runner Process Configuration
N8N_RUNNERS_GRANT_TOKEN=""                # Runner authentication token
N8N_RUNNERS_AUTO_SHUTDOWN_TIMEOUT=15      # Idle shutdown timeout
N8N_RUNNERS_TASK_BROKER_URI="http://127.0.0.1:5679"  # Task broker URI
N8N_RUNNERS_LAUNCHER_HEALTH_CHECK_PORT=5680  # Health check port

# Code Node Security
NODE_FUNCTION_ALLOW_BUILTIN=""            # Allowed built-in modules
NODE_FUNCTION_ALLOW_EXTERNAL=""           # Allowed external modules
N8N_RUNNERS_ALLOW_PROTOTYPE_MUTATION=false  # Allow prototype mutation

# Timezone Configuration
GENERIC_TIMEZONE="America/New_York"       # Default timezone
```

### Execution Data Configuration
```bash
# Execution Data Saving
EXECUTIONS_DATA_SAVE_ON_ERROR="all"       # Save error executions (all/none)
EXECUTIONS_DATA_SAVE_ON_SUCCESS="none"    # Save successful executions 
EXECUTIONS_DATA_SAVE_ON_PROGRESS=true     # Save execution progress
EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false  # Save manual executions

# Execution Timeout
EXECUTIONS_TIMEOUT=3600                   # Global soft timeout (seconds)
EXECUTIONS_TIMEOUT_MAX=7200               # Individual workflow hard timeout
```

### License Configuration Variables
```bash
# License Management
N8N_HIDE_USAGE_PAGE=false                 # Hide usage page
N8N_LICENSE_ACTIVATION_KEY=""             # License activation key
N8N_LICENSE_AUTO_RENEW_ENABLED=true       # Auto-renew license
N8N_LICENSE_DETACH_FLOATING_ON_SHUTDOWN=true  # Release floating licenses on shutdown
N8N_LICENSE_SERVER_URL="https://license.n8n.io/v1"  # License server URL
N8N_LICENSE_TENANT_ID=1                   # License tenant ID
https_proxy_license_server=""             # Proxy for license server (lowercase)
```

### External Storage Configuration (S3)
```bash
# S3 External Storage
N8N_EXTERNAL_STORAGE_S3_HOST=""           # S3 host (e.g., s3.us-east-1.amazonaws.com)
N8N_EXTERNAL_STORAGE_S3_BUCKET_NAME=""    # S3 bucket name
N8N_EXTERNAL_STORAGE_S3_BUCKET_REGION=""  # S3 bucket region
N8N_EXTERNAL_STORAGE_S3_ACCESS_KEY=""     # S3 access key
N8N_EXTERNAL_STORAGE_S3_ACCESS_SECRET=""  # S3 access secret
N8N_EXTERNAL_STORAGE_S3_AUTH_AUTO_DETECT=""  # Auto-detect S3 credentials
```

### Credential Configuration Variables
```bash
# Credential Overwrite
CREDENTIALS_OVERWRITE_DATA=""             # Credential overwrite data
CREDENTIALS_OVERWRITE_ENDPOINT=""         # API endpoint for credentials
CREDENTIALS_DEFAULT_NAME="My credentials" # Default credential name
```

### External Hooks Configuration
```bash
# External Hooks
EXTERNAL_HOOK_FILES=""                    # Backend external hook files (colon-separated)
EXTERNAL_FRONTEND_HOOKS_URLS=""           # Frontend external hook URLs (colon-separated)
```

### LangSmith Integration Variables
```bash
# LangSmith Configuration (must be set globally)
LANGCHAIN_ENDPOINT="https://api.smith.langchain.com"
LANGCHAIN_TRACING_V2=true
LANGCHAIN_API_KEY="YOUR_API_KEY"
```

## B. Docker Deployment Configurations

### Basic Docker Run Command
```bash
# Create persistent volume
docker volume create n8n_data

# Basic n8n Docker container
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  docker.n8n.io/n8nio/n8n
```

### Docker with Environment Variables
```bash
# Docker with custom configuration
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -e N8N_TEMPLATES_ENABLED="false" \
  -e GENERIC_TIMEZONE="Europe/Berlin" \
  -e TZ="Europe/Berlin" \
  -v n8n_data:/home/<USER>/.n8n \
  docker.n8n.io/n8nio/n8n
```

### Docker with PostgreSQL
```bash
# n8n with PostgreSQL database
docker volume create n8n_data
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -e DB_TYPE=postgresdb \
  -e DB_POSTGRESDB_DATABASE=<POSTGRES_DATABASE> \
  -e DB_POSTGRESDB_HOST=<POSTGRES_HOST> \
  -e DB_POSTGRESDB_PORT=<POSTGRES_PORT> \
  -e DB_POSTGRESDB_USER=<POSTGRES_USER> \
  -e DB_POSTGRESDB_SCHEMA=<POSTGRES_SCHEMA> \
  -e DB_POSTGRESDB_PASSWORD=<POSTGRES_PASSWORD> \
  -v n8n_data:/home/<USER>/.n8n \
  docker.n8n.io/n8nio/n8n
```

### Docker with Execution Data Configuration
```bash
# Configure execution data saving
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -e EXECUTIONS_DATA_SAVE_ON_ERROR=all \
  -e EXECUTIONS_DATA_SAVE_ON_SUCCESS=none \
  -e EXECUTIONS_DATA_SAVE_ON_PROGRESS=true \
  -e EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false \
  docker.n8n.io/n8nio/n8n
```

## C. Docker Compose Configurations

### Basic Docker Compose with Traefik
```yaml
# docker-compose.yml
services:
  traefik:
    image: "traefik"
    restart: always
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.web.http.redirections.entryPoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.websecure.address=:443"
      - "--certificatesresolvers.mytlschallenge.acme.tlschallenge=true"
      - "--certificatesresolvers.mytlschallenge.acme.email=${SSL_EMAIL}"
      - "--certificatesresolvers.mytlschallenge.acme.storage=/letsencrypt/acme.json"
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - traefik_data:/letsencrypt
      - /var/run/docker.sock:/var/run/docker.sock:ro

  n8n:
    image: docker.n8n.io/n8nio/n8n
    restart: always
    ports:
      - "127.0.0.1:5678:5678"
    labels:
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(`${SUBDOMAIN}.${DOMAIN_NAME}`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.routers.n8n.entrypoints=web,websecure
      - traefik.http.routers.n8n.tls.certresolver=mytlschallenge
      - traefik.http.middlewares.n8n.headers.SSLRedirect=true
      - traefik.http.middlewares.n8n.headers.STSSeconds=315360000
      - traefik.http.middlewares.n8n.headers.browserXSSFilter=true
      - traefik.http.middlewares.n8n.headers.contentTypeNosniff=true
      - traefik.http.middlewares.n8n.headers.forceSTSHeader=true
      - traefik.http.middlewares.n8n.headers.SSLHost=${DOMAIN_NAME}
      - traefik.http.middlewares.n8n.headers.STSIncludeSubdomains=true
      - traefik.http.middlewares.n8n.headers.STSPreload=true
      - traefik.http.routers.n8n.middlewares=n8n@docker
    environment:
      - N8N_HOST=${SUBDOMAIN}.${DOMAIN_NAME}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_URL=https://${SUBDOMAIN}.${DOMAIN_NAME}/
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE}
    volumes:
      - n8n_data:/home/<USER>/.n8n
      - ./local-files:/files

volumes:
  n8n_data:
  traefik_data:
```

### Docker Compose Environment File (.env)
```bash
# .env file for Docker Compose
# Domain configuration
DOMAIN_NAME=example.com
SUBDOMAIN=n8n
# Results in: https://n8n.example.com

# Timezone configuration
GENERIC_TIMEZONE=Europe/Berlin

# SSL email for Let's Encrypt
SSL_EMAIL=<EMAIL>
```

### Docker Compose with Environment Variables
```yaml
# docker-compose.yml with inline environment variables
n8n:
    image: docker.n8n.io/n8nio/n8n
    environment:
      - N8N_TEMPLATES_ENABLED=false
      - EXECUTIONS_DATA_SAVE_ON_ERROR=all
      - EXECUTIONS_DATA_SAVE_ON_SUCCESS=none
      - EXECUTIONS_DATA_SAVE_ON_PROGRESS=true
      - EXECUTIONS_DATA_SAVE_MANUAL_EXECUTIONS=false
    volumes:
      - n8n_data:/home/<USER>/.n8n
    ports:
      - "5678:5678"
```

## D. Configuration Methods

### 1. Environment Variables (Command Line)
```bash
# Set single variable
export N8N_TEMPLATES_ENABLED=false

# Set multiple variables
export DB_TYPE=postgresdb
export DB_POSTGRESDB_DATABASE=n8n
export DB_POSTGRESDB_HOST=localhost
export DB_POSTGRESDB_PORT=5432
export DB_POSTGRESDB_USER=n8n
export DB_POSTGRESDB_PASSWORD=n8n
export DB_POSTGRESDB_SCHEMA=n8n

# Start n8n
n8n start
```

### 2. File-based Configuration
```bash
# Using _FILE suffix for security
CREDENTIALS_OVERWRITE_DATA_FILE=/path/to/credentials_data
DB_TYPE_FILE=/path/to/db_type
DB_POSTGRESDB_DATABASE_FILE=/path/to/database_name
DB_POSTGRESDB_HOST_FILE=/path/to/database_host
DB_POSTGRESDB_PORT_FILE=/path/to/database_port
DB_POSTGRESDB_USER_FILE=/path/to/database_user
DB_POSTGRESDB_PASSWORD_FILE=/path/to/database_password
DB_POSTGRESDB_SCHEMA_FILE=/path/to/database_schema
DB_POSTGRESDB_SSL_CA_FILE=/path/to/ssl_ca
DB_POSTGRESDB_SSL_CERT_FILE=/path/to/ssl_cert  
DB_POSTGRESDB_SSL_KEY_FILE=/path/to/ssl_key
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED_FILE=/path/to/ssl_reject_unauth
```

### 3. JSON Configuration File
```bash
# Specify configuration file
N8N_CONFIG_FILES=/path/to/config.json

# Example config.json structure
{
  "database": {
    "type": "postgresdb",
    "postgresdb": {
      "host": "localhost",
      "port": 5432,
      "database": "n8n",
      "user": "n8n",
      "password": "n8n",
      "schema": "n8n"
    }
  },
  "credentials": {
    "overwrite": {
      "data": "{\"smtp\": {\"user\": \"<EMAIL>\", \"password\": \"password\"}}"
    }
  }
}
```

## E. Server Setup Examples

### 1. DigitalOcean Droplet Setup
```bash
# Clone n8n Docker Caddy configuration
git clone https://github.com/n8n-io/n8n-docker-caddy.git

# Edit environment variables
nano .env

# Example .env content:
DOMAIN_NAME=your-domain.com
SUBDOMAIN=n8n
GENERIC_TIMEZONE=America/New_York
SSL_EMAIL=<EMAIL>

# Start services
sudo docker compose up -d
```

### 2. Hetzner Setup
```bash
# Clone repository
git clone https://github.com/n8n-io/n8n-docker-caddy.git

# Configure environment
nano .env

# Configure docker-compose.yml if needed
nano docker-compose.yml

# Start n8n and Caddy services
docker compose up -d
```

### 3. Manual PostgreSQL Setup
```bash
# Configure PostgreSQL environment variables
export DB_TYPE=postgresdb
export DB_POSTGRESDB_DATABASE=n8n
export DB_POSTGRESDB_HOST=postgresdb
export DB_POSTGRESDB_PORT=5432
export DB_POSTGRESDB_USER=n8n
export DB_POSTGRESDB_PASSWORD=n8n
export DB_POSTGRESDB_SCHEMA=n8n

# Optional SSL configuration
export DB_POSTGRESDB_SSL_CA=$(pwd)/ca.crt
export DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=false

# Start n8n
n8n start
```

## F. Advanced Configuration Examples

### 1. Base URL Configuration for Separate Frontend/Backend
```bash
# Set frontend API base URL
export VUE_APP_URL_BASE_API=https://n8n.example.com/

# Build n8n-editor-ui package manually
npm run build:frontend
```

### 2. Custom Templates Configuration
```bash
# Use custom workflow templates
N8N_TEMPLATES_HOST=https://your-custom-api.com

# Disable default templates
N8N_TEMPLATES_ENABLED=false
```

### 3. Multi-Main Setup for Queue Mode
```bash
# Enable multi-main setup (requires license)
N8N_MULTI_MAIN_SETUP_ENABLED=true
N8N_MULTI_MAIN_SETUP_KEY_TTL=10
N8N_MULTI_MAIN_SETUP_CHECK_INTERVAL=3

# Configure queue mode
EXECUTIONS_MODE=queue
QUEUE_BULL_REDIS_HOST=redis-server
QUEUE_BULL_REDIS_PORT=6379
QUEUE_BULL_REDIS_PASSWORD=redis-password
```

### 4. Task Runner Configuration
```bash
# Enable internal task runners
N8N_RUNNERS_ENABLED=true
N8N_RUNNERS_MODE=internal
N8N_RUNNERS_MAX_CONCURRENCY=10

# External task runner mode
N8N_RUNNERS_MODE=external  
N8N_RUNNERS_AUTH_TOKEN=your-auth-token
N8N_RUNNERS_TASK_BROKER_URI=http://n8n-main:5679
```

### 5. Security Hardening Configuration
```bash
# Restrict file access
N8N_BLOCK_FILE_ACCESS_TO_N8N_FILES=true
N8N_RESTRICT_FILE_ACCESS_TO="/allowed/directory:/another/allowed"
N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true

# Block environment access in nodes
N8N_BLOCK_ENV_ACCESS_IN_NODE=true

# Secure cookies
N8N_SECURE_COOKIE=true
N8N_SAMESITE_COOKIE=strict

# Disable telemetry
N8N_DIAGNOSTICS_ENABLED=false
```

### 6. Metrics and Monitoring Configuration
```bash
# Enable comprehensive metrics
N8N_METRICS=true
N8N_METRICS_PREFIX=n8n_production_
N8N_METRICS_INCLUDE_DEFAULT_METRICS=true
N8N_METRICS_INCLUDE_CACHE_METRICS=true
N8N_METRICS_INCLUDE_MESSAGE_EVENT_BUS_METRICS=true
N8N_METRICS_INCLUDE_WORKFLOW_ID_LABEL=true
N8N_METRICS_INCLUDE_NODE_TYPE_LABEL=true
N8N_METRICS_INCLUDE_API_ENDPOINTS=true
N8N_METRICS_INCLUDE_API_PATH_LABEL=true
N8N_METRICS_INCLUDE_API_METHOD_LABEL=true
N8N_METRICS_INCLUDE_API_STATUS_CODE_LABEL=true
N8N_METRICS_INCLUDE_QUEUE_METRICS=true
N8N_METRICS_QUEUE_METRICS_INTERVAL=30
```

## G. Best Practices for Production Deployment

### 1. Security Checklist
```bash
# Essential security configurations
N8N_SECURE_COOKIE=true                    # HTTPS-only cookies
N8N_SAMESITE_COOKIE=strict                # Strict cross-site policy
N8N_BLOCK_ENV_ACCESS_IN_NODE=true         # Block env access in Code nodes
N8N_BLOCK_FILE_ACCESS_TO_N8N_FILES=true   # Protect n8n system files
N8N_ENFORCE_SETTINGS_FILE_PERMISSIONS=true # Restrict settings file access
N8N_ENCRYPTION_KEY="your-strong-key"      # Custom encryption key
DB_POSTGRESDB_SSL_ENABLED=true            # Enable database SSL
```

### 2. Performance Optimization
```bash
# Database connection pooling
DB_POSTGRESDB_POOL_SIZE=10
DB_POSTGRESDB_CONNECTION_TIMEOUT=10000
DB_POSTGRESDB_IDLE_CONNECTION_TIMEOUT=20000

# Queue mode for scaling
EXECUTIONS_MODE=queue
QUEUE_BULL_REDIS_HOST=redis-cluster
QUEUE_WORKER_LOCK_DURATION=45000
QUEUE_WORKER_MAX_STALLED_COUNT=2

# Task runners for isolation
N8N_RUNNERS_ENABLED=true
N8N_RUNNERS_MAX_CONCURRENCY=20
N8N_RUNNERS_TASK_TIMEOUT=300
```

### 3. Monitoring and Logging
```bash
# Enable comprehensive monitoring
N8N_METRICS=true
N8N_METRICS_INCLUDE_WORKFLOW_ID_LABEL=true
N8N_METRICS_INCLUDE_NODE_TYPE_LABEL=true
N8N_METRICS_INCLUDE_API_ENDPOINTS=true
N8N_METRICS_INCLUDE_QUEUE_METRICS=true

# Health checks for queue mode
QUEUE_HEALTH_CHECK_ACTIVE=true
QUEUE_HEALTH_CHECK_PORT=8080

# Execution data management
EXECUTIONS_DATA_SAVE_ON_ERROR=all
EXECUTIONS_DATA_SAVE_ON_SUCCESS=none
EXECUTIONS_DATA_SAVE_ON_PROGRESS=false
```

### 4. Backup and Recovery Configuration
```bash
# Configure external storage for binary data
N8N_EXTERNAL_STORAGE_S3_HOST=s3.amazonaws.com
N8N_EXTERNAL_STORAGE_S3_BUCKET_NAME=n8n-backups
N8N_EXTERNAL_STORAGE_S3_BUCKET_REGION=us-west-2
N8N_EXTERNAL_STORAGE_S3_ACCESS_KEY=your-access-key
N8N_EXTERNAL_STORAGE_S3_ACCESS_SECRET=your-secret-key

# Database backup considerations
DB_POSTGRESDB_SSL_ENABLED=true
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=true
```

### 5. High Availability Setup
```bash
# Multi-main configuration
N8N_MULTI_MAIN_SETUP_ENABLED=true
N8N_MULTI_MAIN_SETUP_KEY_TTL=30
N8N_MULTI_MAIN_SETUP_CHECK_INTERVAL=10

# Redis cluster for queue mode
QUEUE_BULL_REDIS_CLUSTER_NODES=redis1:6379,redis2:6379,redis3:6379
QUEUE_BULL_REDIS_TLS=true

# Load balancer configuration
N8N_PROXY_HOPS=2
WEBHOOK_URL=https://n8n.yourdomain.com/
```

## H. Troubleshooting Common Configuration Issues

### 1. Database Connection Issues
```bash
# Test PostgreSQL connection
DB_POSTGRESDB_CONNECTION_TIMEOUT=30000
DB_POSTGRESDB_IDLE_CONNECTION_TIMEOUT=60000
DB_POSTGRESDB_POOL_SIZE=5

# SSL troubleshooting
DB_POSTGRESDB_SSL_ENABLED=true
DB_POSTGRESDB_SSL_REJECT_UNAUTHORIZED=false  # For testing only
```

### 2. Queue Mode Issues
```bash
# Redis connection troubleshooting
QUEUE_BULL_REDIS_TIMEOUT_THRESHOLD=15000
QUEUE_BULL_REDIS_DUALSTACK=true

# Worker configuration
QUEUE_WORKER_LOCK_DURATION=60000
QUEUE_WORKER_STALLED_INTERVAL=60000
QUEUE_WORKER_MAX_STALLED_COUNT=3
```

### 3. Performance Tuning
```bash
# Payload size optimization
N8N_PAYLOAD_SIZE_MAX=32
N8N_FORMDATA_FILE_SIZE_MAX=500

# Execution timeout tuning
EXECUTIONS_TIMEOUT=7200
EXECUTIONS_TIMEOUT_MAX=14400

# Task runner optimization
N8N_RUNNERS_MAX_OLD_SPACE_SIZE=4096
N8N_RUNNERS_MAX_CONCURRENCY=15
```

### 4. SSL/TLS Configuration
```bash
# HTTPS configuration
N8N_PROTOCOL=https
N8N_SSL_KEY=/path/to/private-key.pem
N8N_SSL_CERT=/path/to/certificate.pem
N8N_SECURE_COOKIE=true
```

This comprehensive hosting and configuration guide covers all the essential aspects of deploying and configuring n8n in various environments, from simple Docker setups to complex production deployments with high availability and security considerations.


===============================================================================
5. INTEGRATIONS DOCUMENTATION
===============================================================================

## A. Overview of n8n Integrations

n8n provides extensive integration capabilities through built-in app nodes, core nodes, trigger nodes, and cluster nodes. These integrations allow you to connect with hundreds of services and APIs to create powerful automation workflows.

### Integration Categories

#### 1. Built-in App Nodes
- **Communication & Messaging**: Discord, Slack, Telegram, Microsoft Teams
- **Email Services**: Gmail, Outlook, Mailchimp, SendGrid, Mailgun
- **Cloud Storage**: Google Drive, Dropbox, OneDrive, AWS S3
- **Databases**: PostgreSQL, MySQL, MongoDB, Redis, InfluxDB
- **CRM & Sales**: HubSpot, Salesforce, Pipedrive, Airtable
- **Social Media**: Twitter, LinkedIn, Facebook, Instagram
- **Development**: GitHub, GitLab, Jenkins, Docker
- **Analytics**: Google Analytics, Mixpanel, Segment
- **E-commerce**: Shopify, WooCommerce, Stripe, PayPal
- **Project Management**: Jira, Trello, Asana, Notion, Linear
- **Marketing**: Mailchimp, ActiveCampaign, ConvertKit

#### 2. Core Nodes
- **Data Processing**: Code, Function, Set, Sort, Merge
- **Flow Control**: IF, Switch, Loop Over Items, Wait
- **Utilities**: HTTP Request, Webhook, Schedule Trigger
- **File Operations**: Read/Write Binary File, FTP, SFTP

#### 3. Trigger Nodes
- **Time-based**: Cron Trigger, Interval Trigger
- **Event-based**: Webhook, RSS Trigger, Email Trigger
- **Service-specific**: GitHub Trigger, Slack Trigger, etc.

## B. Comprehensive Built-in App Nodes Directory

### Communication & Messaging Platforms

#### **Discord**
```json
{
  "name": "Discord",
  "path": "builtin/app-nodes/n8n-nodes-base.discord/",
  "capabilities": [
    "Send messages to channels",
    "Send direct messages",
    "Manage servers and channels",
    "Handle webhooks"
  ]
}
```

#### **Slack**
```json
{
  "name": "Slack",
  "path": "builtin/app-nodes/n8n-nodes-base.slack/",
  "capabilities": [
    "Send messages to channels",
    "Send direct messages",
    "Upload files",
    "Manage channels and users",
    "Handle slash commands"
  ]
}
```

#### **Telegram**
```json
{
  "name": "Telegram",
  "path": "builtin/app-nodes/n8n-nodes-base.telegram/",
  "operations": [
    "Send messages",
    "Send photos/documents",
    "Manage bot commands",
    "Handle inline keyboards"
  ]
}
```

#### **Microsoft Teams**
```json
{
  "name": "Microsoft Teams",
  "path": "builtin/app-nodes/n8n-nodes-base.microsoftteams/",
  "capabilities": [
    "Send channel messages",
    "Send chat messages",
    "Manage team channels",
    "Handle notifications"
  ]
}
```

### Email Services

#### **Gmail**
```json
{
  "name": "Gmail",
  "path": "builtin/app-nodes/n8n-nodes-base.gmail/",
  "operations": {
    "draft_operations": "Create, get, delete drafts",
    "label_operations": "Create, get, delete labels",
    "message_operations": "Send, get, reply to messages",
    "thread_operations": "Get, delete, modify threads"
  },
  "common_issues": "Authentication, API limits, formatting"
}
```

#### **Microsoft Outlook**
```json
{
  "name": "Microsoft Outlook",
  "path": "builtin/app-nodes/n8n-nodes-base.microsoftoutlook/",
  "capabilities": [
    "Send emails",
    "Read emails",
    "Manage folders",
    "Handle calendar events"
  ]
}
```

#### **Mailchimp**
```json
{
  "name": "Mailchimp",
  "path": "builtin/app-nodes/n8n-nodes-base.mailchimp/",
  "operations": [
    "Manage subscribers",
    "Create campaigns",
    "Send emails",
    "Manage lists and segments"
  ]
}
```

#### **SendGrid**
```json
{
  "name": "SendGrid",
  "path": "builtin/app-nodes/n8n-nodes-base.sendgrid/",
  "capabilities": [
    "Send transactional emails",
    "Manage contacts",
    "Track email events",
    "Manage templates"
  ]
}
```

### Cloud Storage & File Management

#### **Google Drive**
```json
{
  "name": "Google Drive",
  "path": "builtin/app-nodes/n8n-nodes-base.googledrive/",
  "operations": {
    "file_operations": "Upload, download, delete files",
    "folder_operations": "Create, get, delete folders",
    "shared_drive_operations": "Manage shared drives",
    "file_folder_operations": "Combined file and folder operations"
  },
  "common_issues": "Permission errors, large file handling, API quotas"
}
```

#### **Dropbox**
```json
{
  "name": "Dropbox",
  "path": "builtin/app-nodes/n8n-nodes-base.dropbox/",
  "capabilities": [
    "Upload/download files",
    "Create folders",
    "Share files",
    "Manage file metadata"
  ]
}
```

#### **Microsoft OneDrive**
```json
{
  "name": "Microsoft OneDrive",
  "path": "builtin/app-nodes/n8n-nodes-base.microsoftonedrive/",
  "operations": [
    "File management",
    "Folder operations",
    "Sharing and permissions",
    "Search functionality"
  ]
}
```

#### **AWS S3**
```json
{
  "name": "AWS S3",
  "path": "builtin/app-nodes/n8n-nodes-base.s3/",
  "capabilities": [
    "Upload/download objects",
    "Create/delete buckets",
    "Manage object metadata",
    "Handle presigned URLs"
  ]
}
```

### Databases & Data Storage

#### **PostgreSQL**
```json
{
  "name": "PostgreSQL",
  "path": "builtin/app-nodes/n8n-nodes-base.postgres/",
  "operations": [
    "Execute queries",
    "Insert/update/delete records",
    "Execute stored procedures",
    "Handle transactions"
  ],
  "common_issues": "Connection configuration, query optimization, data types"
}
```

#### **MySQL**
```json
{
  "name": "MySQL",
  "path": "builtin/app-nodes/n8n-nodes-base.mysql/",
  "capabilities": [
    "Execute SQL queries",
    "Manage database records",
    "Handle stored procedures",
    "Database administration tasks"
  ],
  "common_issues": "Connection timeouts, encoding issues, large result sets"
}
```

#### **MongoDB**
```json
{
  "name": "MongoDB",
  "path": "builtin/app-nodes/n8n-nodes-base.mongodb/",
  "operations": [
    "Find documents",
    "Insert/update/delete documents",
    "Aggregate data",
    "Manage collections"
  ]
}
```

#### **Redis**
```json
{
  "name": "Redis",
  "path": "builtin/app-nodes/n8n-nodes-base.redis/",
  "capabilities": [
    "Get/set key-value pairs",
    "List operations",
    "Hash operations",
    "Pub/sub messaging"
  ]
}
```

### CRM & Sales Platforms

#### **HubSpot**
```json
{
  "name": "HubSpot",
  "path": "builtin/app-nodes/n8n-nodes-base.hubspot/",
  "operations": [
    "Manage contacts",
    "Handle deals",
    "Manage companies",
    "Track activities",
    "Custom objects"
  ]
}
```

#### **Salesforce**
```json
{
  "name": "Salesforce",
  "path": "builtin/app-nodes/n8n-nodes-base.salesforce/",
  "capabilities": [
    "Manage leads and opportunities",
    "Handle accounts and contacts",
    "Execute SOQL queries",
    "Manage custom objects"
  ]
}
```

#### **Pipedrive**
```json
{
  "name": "Pipedrive",
  "path": "builtin/app-nodes/n8n-nodes-base.pipedrive/",
  "operations": [
    "Manage deals",
    "Handle persons and organizations",
    "Track activities",
    "Manage pipeline stages"
  ]
}
```

#### **Airtable**
```json
{
  "name": "Airtable",
  "path": "builtin/app-nodes/n8n-nodes-base.airtable/",
  "capabilities": [
    "Create/read/update/delete records",
    "List records with filtering",
    "Manage table structure",
    "Handle attachments"
  ]
}
```

### Development & DevOps

#### **GitHub**
```json
{
  "name": "GitHub",
  "path": "builtin/app-nodes/n8n-nodes-base.github/",
  "operations": [
    "Manage repositories",
    "Handle issues and pull requests",
    "Manage releases",
    "File operations",
    "Organization management"
  ]
}
```

#### **GitLab**
```json
{
  "name": "GitLab",
  "path": "builtin/app-nodes/n8n-nodes-base.gitlab/",
  "capabilities": [
    "Project management",
    "Issue tracking",
    "Merge request handling",
    "CI/CD pipeline management"
  ]
}
```

#### **Jenkins**
```json
{
  "name": "Jenkins",
  "path": "builtin/app-nodes/n8n-nodes-base.jenkins/",
  "operations": [
    "Trigger builds",
    "Get build status",
    "Manage jobs",
    "Handle build artifacts"
  ]
}
```

### Google Workspace Integration

#### **Google Sheets**
```json
{
  "name": "Google Sheets",
  "path": "builtin/app-nodes/n8n-nodes-base.googlesheets/",
  "operations": {
    "document_operations": "Create, delete, get spreadsheets",
    "sheet_operations": "Manage individual sheets",
    "cell_operations": "Read/write cell data",
    "row_operations": "Add, delete, update rows"
  },
  "common_issues": "API quotas, formula handling, data formatting"
}
```

#### **Google Calendar**
```json
{
  "name": "Google Calendar",
  "path": "builtin/app-nodes/n8n-nodes-base.googlecalendar/",
  "operations": {
    "calendar_operations": "Create, delete, get calendars",
    "event_operations": "Create, update, delete events"
  }
}
```

#### **Google Docs**
```json
{
  "name": "Google Docs",
  "path": "builtin/app-nodes/n8n-nodes-base.googledocs/",
  "capabilities": [
    "Create documents",
    "Read document content",
    "Update document text",
    "Manage document permissions"
  ]
}
```

#### **Google Cloud Services**
```json
{
  "services": {
    "Google Cloud Firestore": "builtin/app-nodes/n8n-nodes-base.googlecloudfirestore/",
    "Google Cloud Storage": "builtin/app-nodes/n8n-nodes-base.googlecloudstorage/",
    "Google Cloud Natural Language": "builtin/app-nodes/n8n-nodes-base.googlecloudnaturallanguage/",
    "Google BigQuery": "builtin/app-nodes/n8n-nodes-base.googlebigquery/"
  }
}
```

### E-commerce & Payment Platforms

#### **Shopify**
```json
{
  "name": "Shopify",
  "path": "builtin/app-nodes/n8n-nodes-base.shopify/",
  "operations": [
    "Manage products",
    "Handle orders",
    "Manage customers",
    "Inventory management",
    "Fulfillment operations"
  ]
}
```

#### **Stripe**
```json
{
  "name": "Stripe",
  "path": "builtin/app-nodes/n8n-nodes-base.stripe/",
  "capabilities": [
    "Process payments",
    "Manage customers",
    "Handle subscriptions",
    "Manage invoices",
    "Webhook handling"
  ]
}
```

#### **PayPal**
```json
{
  "name": "PayPal",
  "path": "builtin/app-nodes/n8n-nodes-base.paypal/",
  "operations": [
    "Process payments",
    "Manage invoices",
    "Handle refunds",
    "Track transactions"
  ]
}
```

### Project Management & Productivity

#### **Notion**
```json
{
  "name": "Notion",
  "path": "builtin/app-nodes/n8n-nodes-base.notion/",
  "operations": [
    "Manage databases",
    "Create/update pages",
    "Query database records",
    "Handle blocks and content"
  ],
  "common_issues": "API rate limits, complex data structures, permissions"
}
```

#### **Jira**
```json
{
  "name": "Jira",
  "path": "builtin/app-nodes/n8n-nodes-base.jira/",
  "capabilities": [
    "Manage issues",
    "Handle projects",
    "Track user activities",
    "Custom field management"
  ]
}
```

#### **Trello**
```json
{
  "name": "Trello",
  "path": "builtin/app-nodes/n8n-nodes-base.trello/",
  "operations": [
    "Manage boards and lists",
    "Create/update cards",
    "Handle attachments",
    "Manage members and labels"
  ]
}
```

#### **Asana**
```json
{
  "name": "Asana",
  "path": "builtin/app-nodes/n8n-nodes-base.asana/",
  "capabilities": [
    "Manage projects and tasks",
    "Handle team collaboration",
    "Track time and progress",
    "Manage portfolios"
  ]
}
```

### AI & Machine Learning Services

#### **OpenAI**
```json
{
  "name": "OpenAI",
  "path": "builtin/app-nodes/n8n-nodes-langchain.openai/",
  "operations": {
    "assistant_operations": "Create and manage AI assistants",
    "audio_operations": "Speech-to-text, text-to-speech",
    "file_operations": "Upload and manage files",
    "image_operations": "Generate and analyze images",
    "text_operations": "Chat completions, embeddings"
  },
  "common_issues": "API rate limits, token usage, model availability"
}
```

#### **Perplexity**
```json
{
  "name": "Perplexity",
  "path": "builtin/app-nodes/n8n-nodes-langchain.perplexity/",
  "capabilities": [
    "AI-powered search",
    "Question answering",
    "Information synthesis"
  ]
}
```

#### **Humantic AI**
```json
{
  "name": "Humantic AI",
  "path": "builtin/app-nodes/n8n-nodes-base.humanticai/",
  "operations": [
    "Personality analysis",
    "Behavioral insights",
    "Sales intelligence"
  ]
}
```

### Analytics & Monitoring

#### **Google Analytics**
```json
{
  "name": "Google Analytics",
  "path": "builtin/app-nodes/n8n-nodes-base.googleanalytics/",
  "capabilities": [
    "Retrieve website analytics",
    "Generate reports",
    "Track user behavior",
    "Monitor conversions"
  ]
}
```

#### **Mixpanel**
```json
{
  "name": "Mixpanel",
  "path": "builtin/app-nodes/n8n-nodes-base.mixpanel/",
  "operations": [
    "Track events",
    "Manage user profiles",
    "Export data",
    "Create funnels and cohorts"
  ]
}
```

#### **PostHog**
```json
{
  "name": "PostHog",
  "path": "builtin/app-nodes/n8n-nodes-base.posthog/",
  "capabilities": [
    "Product analytics",
    "Feature flags",
    "Session recordings",
    "A/B testing"
  ]
}
```

### Additional Integrations

#### **Specialized Databases**
```json
{
  "QuestDB": "builtin/app-nodes/n8n-nodes-base.questdb/",
  "InfluxDB": "builtin/app-nodes/n8n-nodes-base.influxdb/",
  "Snowflake": "builtin/app-nodes/n8n-nodes-base.snowflake/",
  "Supabase": "builtin/app-nodes/n8n-nodes-base.supabase/"
}
```

#### **Communication Tools**
```json
{
  "Mattermost": "builtin/app-nodes/n8n-nodes-base.mattermost/",
  "Rocket.Chat": "builtin/app-nodes/n8n-nodes-base.rocketchat/",
  "Matrix": "builtin/app-nodes/n8n-nodes-base.matrix/"
}
```

#### **Marketing Automation**
```json
{
  "ActiveCampaign": "builtin/app-nodes/n8n-nodes-base.activecampaign/",
  "ConvertKit": "builtin/app-nodes/n8n-nodes-base.convertkit/",
  "Mailgun": "builtin/app-nodes/n8n-nodes-base.mailgun/",
  "Mailjet": "builtin/app-nodes/n8n-nodes-base.mailjet/"
}
```

#### **Social Media**
```json
{
  "Twitter": "builtin/app-nodes/n8n-nodes-base.twitter/",
  "LinkedIn": "builtin/app-nodes/n8n-nodes-base.linkedin/",
  "Reddit": "builtin/app-nodes/n8n-nodes-base.reddit/",
  "Medium": "builtin/app-nodes/n8n-nodes-base.medium/"
}
```

## C. Integration Configuration Examples

### Basic Node Configuration Pattern
```json
{
  "parameters": {
    "authentication": "serviceAccount",
    "resource": "spreadsheet",
    "operation": "read",
    "spreadsheetId": "your-spreadsheet-id",
    "range": "Sheet1!A1:D10"
  },
  "credentials": {
    "googleSheetsOAuth2Api": {
      "id": "credential-id",
      "name": "Google Sheets Account"
    }
  }
}
```

### HTTP Request Node for Custom Integrations
```json
{
  "parameters": {
    "url": "https://api.example.com/data",
    "method": "POST",
    "authentication": "headerAuth",
    "sendHeaders": true,
    "headerParameters": {
      "parameters": [
        {
          "name": "Authorization",
          "value": "Bearer {{ $credentials.apiKey }}"
        },
        {
          "name": "Content-Type",
          "value": "application/json"
        }
      ]
    },
    "sendBody": true,
    "bodyContentType": "json",
    "jsonBody": {
      "data": "{{ $json.inputData }}",
      "timestamp": "{{ $now }}"
    }
  }
}
```

### Webhook Configuration for Event-Driven Workflows
```json
{
  "parameters": {
    "httpMethod": "POST",
    "path": "webhook-endpoint",
    "responseMode": "responseNode",
    "options": {
      "rawBody": false,
      "allowedOrigins": "*"
    }
  },
  "webhookDescription": {
    "restartWebhook": true,
    "handshakeEnabled": false
  }
}
```

## D. Common Integration Patterns

### 1. Data Synchronization Pattern
```javascript
// Pattern: Source -> Transform -> Destination
const syncPattern = {
  "source": "Airtable (Read records)",
  "transform": "Code Node (Data processing)",
  "destination": "Google Sheets (Write data)",
  "scheduling": "Cron Trigger (Every hour)"
};
```

### 2. Event-Driven Automation Pattern
```javascript
// Pattern: Trigger -> Condition -> Action -> Notification
const eventPattern = {
  "trigger": "GitHub Webhook (New pull request)",
  "condition": "IF Node (Check criteria)",
  "action": "Jira (Create issue)",
  "notification": "Slack (Send message)"
};
```

### 3. Multi-Service Integration Pattern
```javascript
// Pattern: Multiple sources -> Merge -> Process -> Multiple destinations
const multiServicePattern = {
  "sources": ["Shopify Orders", "PayPal Transactions", "Stripe Payments"],
  "merge": "Merge Node (Combine data)",
  "process": "Code Node (Calculate totals)",
  "destinations": ["Google Sheets", "Slack Notification", "Email Report"]
};
```

### 4. AI-Enhanced Data Processing Pattern
```javascript
// Pattern: Data Input -> AI Processing -> Enhanced Output
const aiPattern = {
  "input": "Gmail (Read emails)",
  "ai_processing": "OpenAI (Sentiment analysis)",
  "enrichment": "Code Node (Add metadata)",
  "output": "Notion (Create pages with insights)"
};
```

## E. Best Practices for Integrations

### 1. Authentication Management
```json
{
  "best_practices": {
    "credential_security": "Use separate credentials for different environments",
    "oauth_refresh": "Handle OAuth token refresh automatically",
    "api_keys": "Rotate API keys regularly",
    "permissions": "Use least privilege principle"
  }
}
```

### 2. Error Handling and Retry Logic
```javascript
// Implement robust error handling
const errorHandlingPattern = {
  "try_catch": "Wrap API calls in try-catch blocks",
  "retry_logic": "Implement exponential backoff for transient errors",
  "error_notifications": "Send alerts for persistent failures",
  "fallback_strategies": "Have alternative data sources or actions"
};
```

### 3. Rate Limiting and Performance
```json
{
  "rate_limiting": {
    "batch_requests": "Process items in batches to avoid rate limits",
    "wait_nodes": "Add delays between requests when needed",
    "parallel_processing": "Use multiple paths for independent operations",
    "cache_responses": "Cache frequently accessed data"
  }
}
```

### 4. Data Transformation and Validation
```javascript
// Data validation and transformation patterns
const dataPatterns = {
  "validation": {
    "required_fields": "Check for required fields before processing",
    "data_types": "Validate data types and formats",
    "business_rules": "Apply business logic validation"
  },
  "transformation": {
    "mapping": "Map between different data schemas",
    "formatting": "Standardize date/time formats and data structures",
    "enrichment": "Add metadata and calculated fields"
  }
};
```

## F. Troubleshooting Common Integration Issues

### 1. Authentication Problems
```json
{
  "oauth_issues": {
    "symptoms": "401 Unauthorized, token expired errors",
    "solutions": [
      "Refresh OAuth tokens",
      "Check credential configuration",
      "Verify API permissions",
      "Update authentication scopes"
    ]
  },
  "api_key_issues": {
    "symptoms": "403 Forbidden, invalid key errors",
    "solutions": [
      "Verify API key is correct",
      "Check key permissions",
      "Confirm API endpoint URLs",
      "Review rate limiting settings"
    ]
  }
}
```

### 2. Data Format Issues
```json
{
  "format_mismatches": {
    "symptoms": "Data not appearing, format errors",
    "solutions": [
      "Check data type compatibility",
      "Use Code node for transformation",
      "Validate JSON structure",
      "Handle null/undefined values"
    ]
  }
}
```

### 3. Rate Limiting Issues
```json
{
  "rate_limit_errors": {
    "symptoms": "429 Too Many Requests, quota exceeded",
    "solutions": [
      "Implement request batching",
      "Add Wait nodes between requests",
      "Use queue mode for scaling",
      "Monitor API usage quotas"
    ]
  }
}
```

### 4. Network and Connectivity Issues
```json
{
  "connection_problems": {
    "symptoms": "Timeouts, connection refused errors",
    "solutions": [
      "Check network connectivity",
      "Verify firewall settings",
      "Increase timeout values",
      "Use proxy configuration if needed"
    ]
  }
}
```

## G. Advanced Integration Techniques

### 1. Custom Node Development
```javascript
// Creating custom nodes for specialized integrations
const customNodeStructure = {
  "node_definition": "INodeTypeDescription with operations and parameters",
  "credential_support": "Custom credential types for authentication",
  "execute_method": "Logic for API interactions and data processing",
  "error_handling": "Comprehensive error management",
  "testing": "Unit tests and integration tests"
};
```

### 2. Webhook Security
```json
{
  "webhook_security": {
    "signature_verification": "Verify webhook signatures from providers",
    "ip_whitelisting": "Restrict webhook sources by IP",
    "rate_limiting": "Implement webhook rate limiting",
    "payload_validation": "Validate incoming webhook payloads"
  }
}
```

### 3. Environment-Specific Configurations
```json
{
  "environment_management": {
    "development": "Use test API endpoints and sandbox accounts",
    "staging": "Mirror production setup with test data",
    "production": "Use production credentials and endpoints",
    "configuration": "Environment-specific variable management"
  }
}
```

This comprehensive integrations documentation provides a complete overview of n8n's extensive integration capabilities, covering hundreds of built-in services and best practices for implementing robust, scalable automation workflows.


===============================================================================
6. API AND ADVANCED FEATURES
===============================================================================

## A. n8n REST API Overview

The n8n REST API provides programmatic access to workflows, credentials, executions, and other n8n resources. It supports full CRUD operations and advanced features like pagination, filtering, and batch operations.

### API Base URLs and Access
```bash
# Self-hosted instance
https://<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version>/

# n8n Cloud instance
https://<your-cloud-instance>/api/v<version>/

# API Playground (Swagger UI)
https://<N8N_HOST>:<N8N_PORT>/<N8N_PATH>/api/v<version>/docs
```

### Authentication
```bash
# API Key Authentication
curl -H "X-N8N-API-KEY: <your-api-key>" \
     -H "Content-Type: application/json" \
     https://your-n8n-instance/api/v1/workflows
```

### API Security Configuration
```bash
# Disable public API
export N8N_PUBLIC_API_DISABLED=true

# Disable API playground (Swagger UI)
export N8N_PUBLIC_API_SWAGGERUI_DISABLED=true

# Custom API endpoint path
export N8N_ENDPOINT_REST=api

# Enable API metrics
export N8N_METRICS_INCLUDE_API_ENDPOINTS=true
export N8N_METRICS_INCLUDE_API_PATH_LABEL=true
export N8N_METRICS_INCLUDE_API_METHOD_LABEL=true
export N8N_METRICS_INCLUDE_API_STATUS_CODE_LABEL=true
```

## B. Workflows API

### Get Workflows with Pagination
```bash
# Get first page of active workflows
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/workflows?active=true&limit=150' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: your-api-key'

# Get next page using cursor
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/workflows?active=true&limit=150&cursor=MTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA' \
  -H 'accept: application/json' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Workflow API Response Structure
```json
{
  "data": [
    {
      "id": "1012",
      "name": "My Workflow",
      "active": false,
      "nodes": [
        {
          "parameters": {},
          "name": "Start",
          "type": "n8n-nodes-base.start",
          "typeVersion": 1,
          "position": [130, 640]
        }
      ],
      "connections": {
        "Start": {
          "main": [
            [
              {
                "node": "HTTP Request",
                "type": "main",
                "index": 0
              }
            ]
          ]
        }
      },
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  ],
  "nextCursor": "MTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA"
}
```

### Create Workflow
```bash
# Create new workflow
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/workflows' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "New Workflow",
    "active": false,
    "nodes": [
      {
        "parameters": {},
        "name": "Start",
        "type": "n8n-nodes-base.manualTrigger",
        "typeVersion": 1,
        "position": [100, 200]
      }
    ],
    "connections": {}
  }'
```

### Update Workflow
```bash
# Activate workflow
curl -X 'PATCH' \
  'https://your-n8n-instance/api/v1/workflows/1012' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{"active": true}'

# Update workflow content
curl -X 'PUT' \
  'https://your-n8n-instance/api/v1/workflows/1012' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "Updated Workflow",
    "active": true,
    "nodes": [...],
    "connections": {...}
  }'
```

### Get Single Workflow
```bash
# Get workflow by ID
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/workflows/1012' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Delete Workflow
```bash
# Delete workflow
curl -X 'DELETE' \
  'https://your-n8n-instance/api/v1/workflows/1012' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Filter Workflows by Project (v1.53.0+)
```bash
# Get workflows in specific project
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/workflows?project=project-id' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Transfer Workflows (v1.53.0+)
```bash
# Transfer workflow to another project
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/workflows/1012/transfer' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{"projectId": "target-project-id"}'
```

## C. Credentials API

### Create Credentials
```bash
# Create new credentials
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/credentials' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "MyAirtable",
    "type": "airtableApi",
    "nodesAccess": [
      {
        "nodeType": "n8n-nodes-base.airtable"
      }
    ],
    "data": {
      "apiKey": "your-api-key"
    }
  }'
```

### Credentials API Response
```json
{
  "data": {
    "name": "MyAirtable",
    "type": "airtableApi",
    "data": {
      "apiKey": "your-api-key"
    },
    "nodesAccess": [
      {
        "nodeType": "n8n-nodes-base.airtable",
        "date": "2023-01-01T00:00:00.000Z"
      }
    ],
    "id": "29",
    "createdAt": "2023-01-01T00:00:00.000Z",
    "updatedAt": "2023-01-01T00:00:00.000Z"
  }
}
```

### Get Credential Schema
```bash
# Get credential type schema
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/credentials/schema/airtableApi' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Credential Overwrite via API
```bash
# Set up credential overwrite endpoint
export CREDENTIALS_OVERWRITE_ENDPOINT=send-credentials

# Send credentials via custom endpoint
curl -H "Content-Type: application/json" \
     --data @oauth-credentials.json \
     http://localhost:5678/send-credentials
```

### Credential Overwrite JSON Structure
```json
{
  "asanaOAuth2Api": {
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret"
  },
  "githubOAuth2Api": {
    "clientId": "your-client-id",
    "clientSecret": "your-client-secret"
  }
}
```

## D. Variables API (v1.53.0+)

### Create Variables
```bash
# Create new variable
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/variables' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "key": "API_BASE_URL",
    "value": "https://api.example.com",
    "type": "string"
  }'
```

### Read Variables
```bash
# Get all variables
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/variables' \
  -H 'X-N8N-API-KEY: your-api-key'

# Get specific variable
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/variables/API_BASE_URL' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Delete Variables
```bash
# Delete variable
curl -X 'DELETE' \
  'https://your-n8n-instance/api/v1/variables/API_BASE_URL' \
  -H 'X-N8N-API-KEY: your-api-key'
```

## E. Projects API (v1.54.0+)

### Create Projects
```bash
# Create new project
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/projects' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "Marketing Automation",
    "type": "team"
  }'
```

### Read Projects
```bash
# Get all projects
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/projects' \
  -H 'X-N8N-API-KEY: your-api-key'

# Get specific project
curl -X 'GET' \
  'https://your-n8n-instance/api/v1/projects/project-id' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Update Projects
```bash
# Update project
curl -X 'PUT' \
  'https://your-n8n-instance/api/v1/projects/project-id' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "Updated Project Name",
    "type": "team"
  }'
```

### Delete Projects
```bash
# Delete project
curl -X 'DELETE' \
  'https://your-n8n-instance/api/v1/projects/project-id' \
  -H 'X-N8N-API-KEY: your-api-key'
```

## F. User Roles API (v1.54.0+)

### Create User Roles
```bash
# Create new role
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/roles' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "name": "Marketing Manager",
    "scope": "project",
    "permissions": [
      "workflow:create",
      "workflow:read",
      "workflow:update",
      "workflow:execute"
    ]
  }'
```

### Update User Roles
```bash
# Update role permissions
curl -X 'PUT' \
  'https://your-n8n-instance/api/v1/roles/role-id' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "permissions": [
      "workflow:create",
      "workflow:read",
      "workflow:update",
      "workflow:execute",
      "credential:read"
    ]
  }'
```

### Delete User Roles
```bash
# Delete role
curl -X 'DELETE' \
  'https://your-n8n-instance/api/v1/roles/role-id' \
  -H 'X-N8N-API-KEY: your-api-key'
```

## G. Project User Management API

### Assign User to Project
```bash
# Assign user to project with role
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/projects/project-id/users' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "userId": "user-id",
    "role": "member"
  }'
```

### Update User Role in Project
```bash
# Update user role in project
curl -X 'PUT' \
  'https://your-n8n-instance/api/v1/projects/project-id/users/user-id' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "role": "admin"
  }'
```

### Remove User from Project
```bash
# Remove user from project
curl -X 'DELETE' \
  'https://your-n8n-instance/api/v1/projects/project-id/users/user-id' \
  -H 'X-N8N-API-KEY: your-api-key'
```

## H. Scoped API Keys (Enterprise)

### API Key Scopes and Permissions
```json
{
  "scoped_api_keys": {
    "access_types": ["read", "write", "both"],
    "resource_scopes": {
      "variables": ["list", "create", "delete"],
      "security_audit": ["generate_reports"],
      "projects": ["list", "create", "update", "delete"],
      "executions": ["list", "read", "delete"],
      "credentials": ["list", "create", "update", "delete", "move"],
      "workflows": ["list", "create", "update", "delete", "move", "add_remove_tags"]
    }
  }
}
```

### Create Scoped API Key
```bash
# Create API key with specific scopes
curl -X 'POST' \
  'https://your-n8n-instance/api/v1/api-keys' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-admin-api-key' \
  -d '{
    "name": "Marketing Team API Key",
    "access": "read",
    "scopes": {
      "workflows": ["list", "read"],
      "executions": ["list", "read"],
      "projects": ["list"]
    }
  }'
```

## I. Pagination Implementation

### Basic Pagination Pattern
```json
{
  "pagination_response": {
    "data": [
      "// Array of resources"
    ],
    "nextCursor": "MTIzZTQ1NjctZTg5Yi0xMmQzLWE0NTYtNDI2NjE0MTc0MDA"
  }
}
```

### HTTP Node Pagination Configuration

#### Next URL from Response
```javascript
// Configuration for response-based pagination
{
  "paginationMode": "responseContainsNextUrl",
  "nextUrlExpression": "{{ $response.body['next-page'] }}"
}
```

#### Increment Page Number (Query Parameter)
```javascript
// Configuration for query parameter pagination
{
  "paginationMode": "updateParameterInEachRequest",
  "parameterType": "query",
  "parameterName": "page",
  "parameterValue": "{{ $pageCount + 1 }}"
}
```

#### Increment Page Number (Request Body)
```javascript
// Configuration for body parameter pagination
{
  "httpMethod": "POST",
  "paginationMode": "updateParameterInEachRequest", 
  "parameterType": "body",
  "parameterName": "page",
  "parameterValue": "{{ $pageCount + 1 }}"
}
```

### HTTP Node Variables for Pagination
```javascript
// Special variables available in HTTP node expressions
const httpNodeVariables = {
  "$pageCount": "Number of pages fetched so far",
  "$request": "Request object sent by HTTP node",
  "$response": {
    "body": "Response body from API",
    "headers": "Response headers",
    "statusCode": "HTTP status code"
  }
};
```

## J. Rate Limiting and Performance Optimization

### Built-in HTTP Request Batching
```json
{
  "http_request_batching": {
    "items_per_batch": 10,
    "batch_interval_ms": 1000,
    "description": "Send requests in smaller batches with delays"
  }
}
```

### Manual Rate Limiting with Loop and Wait
```json
{
  "manual_rate_limiting": {
    "workflow_pattern": [
      "Loop Over Items (Batch input)",
      "HTTP Request (API call)",
      "Wait (Delay between requests)",
      "Loop back to Loop Over Items"
    ]
  }
}
```

### Rate Limiting Workflow Example
```json
{
  "rate_limit_workflow": {
    "nodes": [
      {
        "name": "Manual Trigger",
        "type": "n8n-nodes-base.manualTrigger"
      },
      {
        "name": "Loop Over Items",
        "type": "n8n-nodes-base.splitInBatches",
        "parameters": {
          "batchSize": 500
        }
      },
      {
        "name": "OpenAI",
        "type": "n8n-nodes-base.openAi",
        "parameters": {
          "resource": "chat"
        }
      },
      {
        "name": "Wait",
        "type": "n8n-nodes-base.wait",
        "parameters": {
          "unit": "minutes",
          "amount": 1
        }
      }
    ],
    "connections": {
      "Loop Over Items": {
        "main": [
          [null],
          [{"node": "OpenAI", "type": "main", "index": 0}]
        ]
      },
      "OpenAI": {
        "main": [[{"node": "Wait", "type": "main", "index": 0}]]
      },
      "Wait": {
        "main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]
      }
    }
  }
}
```

## K. Webhook Management and Security

### Webhook Endpoint Configuration
```bash
# Custom webhook paths
export N8N_ENDPOINT_WEBHOOK=webhook
export N8N_ENDPOINT_WEBHOOK_TEST=webhook-test
export N8N_ENDPOINT_WEBHOOK_WAIT=webhook-waiting

# Manual webhook URL override
export WEBHOOK_URL=https://your-domain.com/

# Webhook payload limits
export N8N_PAYLOAD_SIZE_MAX=16          # Max payload size in MiB
export N8N_FORMDATA_FILE_SIZE_MAX=200   # Max form-data file size in MiB
```

### Webhook Security Best Practices
```json
{
  "webhook_security": {
    "signature_verification": "Verify webhook signatures from providers",
    "ip_whitelisting": "Restrict webhook sources by IP address",
    "rate_limiting": "Implement webhook-specific rate limiting",
    "payload_validation": "Validate incoming webhook payloads",
    "https_only": "Use HTTPS for all webhook endpoints"
  }
}
```

### Webhook Response Handling
```javascript
// Access webhook execution resume URL
const resumeUrl = $execution.resumeUrl;

// Webhook data access patterns
const webhookData = {
  "headers": $json.headers,
  "body": $json.body,
  "query": $json.query,
  "params": $json.params
};
```

## L. Template Library API

### Custom Template Host Configuration
```bash
# Set custom templates host
export N8N_TEMPLATES_HOST=https://your-custom-api.com

# Disable default templates
export N8N_TEMPLATES_ENABLED=false
```

### Required Template API Endpoints
```bash
# Endpoints your custom template API must provide
GET  /templates/workflows/<id>          # Get workflow template by ID
GET  /templates/search                  # Search workflow templates
GET  /templates/collections/<id>        # Get collection by ID
GET  /templates/collections             # List all collections
GET  /templates/categories              # List all categories
GET  /health                           # Health check endpoint
```

### Template Search API Parameters
```json
{
  "search_parameters": {
    "page": "integer - Page of results to return",
    "rows": "integer - Maximum results per page", 
    "category": "string - Comma-separated categories",
    "search": "string - Search query term"
  }
}
```

### Example Template API Usage
```bash
# Search templates
curl 'https://api.n8n.io/templates/search?page=1&rows=20&search=automation'

# Get categories
curl 'https://api.n8n.io/templates/categories'

# Get collections
curl 'https://api.n8n.io/templates/collections'

# Health check
curl 'https://api.n8n.io/health'
```

## M. Source Control and Environment Management

### GitHub Actions for n8n Pull
```yaml
# .github/workflows/n8n-pull.yml
name: n8n Production Pull
on:
  push:
    branches: ["production"]
  workflow_dispatch:

jobs:
  run-pull:
    runs-on: ubuntu-latest
    steps:
      - name: Pull Changes to n8n
        run: >
          curl --location '${{ secrets.INSTANCE_URL }}/version-control/pull'
          --header 'Content-Type: application/json'
          --header 'X-N8N-API-KEY: ${{ secrets.INSTANCE_API_KEY }}'
```

### Environment Management API
```bash
# Pull changes from version control
curl -X 'POST' \
  'https://your-n8n-instance/version-control/pull' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key'

# Push changes to version control
curl -X 'POST' \
  'https://your-n8n-instance/version-control/push' \
  -H 'Content-Type: application/json' \
  -H 'X-N8N-API-KEY: your-api-key' \
  -d '{
    "message": "Updated workflow configurations",
    "fileNames": ["workflow1.json", "workflow2.json"]
  }'
```

## N. Advanced API Features

### Execution Resume URLs
```javascript
// Get workflow resume URL for Wait nodes
const resumeUrl = $execution.resumeUrl;

// Use in webhook or external system to resume workflow
// POST to resumeUrl with optional data
```

### API Metrics and Monitoring
```bash
# Enable comprehensive API metrics
export N8N_METRICS=true
export N8N_METRICS_INCLUDE_API_ENDPOINTS=true
export N8N_METRICS_INCLUDE_API_PATH_LABEL=true
export N8N_METRICS_INCLUDE_API_METHOD_LABEL=true
export N8N_METRICS_INCLUDE_API_STATUS_CODE_LABEL=true

# Access metrics endpoint
curl 'https://your-n8n-instance/metrics' \
  -H 'X-N8N-API-KEY: your-api-key'
```

### Custom API Endpoint Prefixes
```bash
# Custom endpoint paths
export N8N_ENDPOINT_REST=custom-api
export N8N_PUBLIC_API_ENDPOINT=custom-public-api

# Results in:
# https://your-instance/custom-api/workflows
# https://your-instance/custom-public-api/workflows
```

## O. Error Handling and Debugging

### Common API Error Responses
```json
{
  "error_examples": {
    "401_unauthorized": {
      "error": "Unauthorized",
      "message": "Invalid API key or token expired"
    },
    "403_forbidden": {
      "error": "Forbidden", 
      "message": "Insufficient permissions for this operation"
    },
    "404_not_found": {
      "error": "Not Found",
      "message": "Workflow with ID 'xyz' not found"
    },
    "422_validation_error": {
      "error": "Validation Error",
      "message": "Invalid workflow structure",
      "details": [
        {
          "field": "nodes",
          "message": "At least one node is required"
        }
      ]
    },
    "429_rate_limit": {
      "error": "Rate Limit Exceeded",
      "message": "Too many requests",
      "retryAfter": 60
    }
  }
}
```

### API Debugging Techniques
```bash
# Enable verbose curl output
curl -v -X 'GET' \
  'https://your-n8n-instance/api/v1/workflows' \
  -H 'X-N8N-API-KEY: your-api-key'

# Log API responses
curl -w "@curl-format.txt" \
  'https://your-n8n-instance/api/v1/workflows' \
  -H 'X-N8N-API-KEY: your-api-key'

# Check API health
curl 'https://your-n8n-instance/health'
```

### Webhook Debugging
```javascript
// Debug webhook data
console.log('Webhook Headers:', $json.headers);
console.log('Webhook Body:', $json.body);
console.log('Webhook Query:', $json.query);

// Test webhook endpoints
const testWebhookUrl = 'https://your-n8n-instance/webhook-test/your-webhook-id';
const prodWebhookUrl = 'https://your-n8n-instance/webhook/your-webhook-id';
```

This comprehensive API and advanced features documentation covers all major aspects of programmatic interaction with n8n, from basic CRUD operations to advanced features like pagination, rate limiting, webhook security, and environment management.


===============================================================================
7. SPECIALIZED INTEGRATION EXAMPLES
===============================================================================

## A. Google Cloud Platform Integration Suite

### Google Cloud Storage Integration

#### Overview
The Google Cloud Storage integration enables comprehensive object storage management, file operations, and automated backup workflows within n8n.

#### Key Operations
```json
{
  "google_cloud_storage_operations": {
    "bucket_management": [
      "Create buckets",
      "List buckets", 
      "Delete buckets",
      "Set bucket policies"
    ],
    "object_operations": [
      "Upload objects",
      "Download objects", 
      "Delete objects",
      "Copy objects",
      "List objects"
    ],
    "metadata_management": [
      "Set object metadata",
      "Get object metadata",
      "Update object properties"
    ],
    "access_control": [
      "Set object ACLs",
      "Generate signed URLs",
      "Manage bucket permissions"
    ]
  }
}
```

#### Example Workflow: Automated File Backup System
```json
{
  "workflow_name": "Automated GCS Backup System",
  "description": "Monitor local directory changes and backup to Google Cloud Storage",
  "nodes": [
    {
      "name": "File System Trigger",
      "type": "n8n-nodes-base.filesystemTrigger",
      "parameters": {
        "path": "/data/uploads",
        "events": ["add", "modify"]
      }
    },
    {
      "name": "Google Cloud Storage",
      "type": "n8n-nodes-base.googleCloudStorage",
      "parameters": {
        "operation": "upload",
        "bucketName": "backup-bucket",
        "fileName": "{{ $json.filename }}",
        "binaryData": true
      }
    },
    {
      "name": "Slack Notification",
      "type": "n8n-nodes-base.slack",
      "parameters": {
        "channel": "#backups",
        "text": "File {{ $json.filename }} backed up to GCS"
      }
    }
  ]
}
```

### Google Tasks Integration

#### Overview
The Google Tasks integration provides comprehensive task and task list management capabilities for productivity automation workflows.

#### Key Operations
```json
{
  "google_tasks_operations": {
    "task_lists": [
      "Create task lists",
      "Get task lists",
      "Update task lists",
      "Delete task lists"
    ],
    "tasks": [
      "Create tasks",
      "Get tasks",
      "Update tasks",
      "Delete tasks",
      "Mark tasks complete"
    ],
    "task_properties": [
      "Set due dates",
      "Add notes",
      "Set priorities",
      "Create subtasks"
    ]
  }
}
```

#### Example Workflow: Project Management Automation
```json
{
  "workflow_name": "GitHub Issues to Google Tasks",
  "description": "Convert GitHub issues to Google Tasks for project management",
  "nodes": [
    {
      "name": "GitHub Trigger",
      "type": "n8n-nodes-base.githubTrigger",
      "parameters": {
        "events": ["issues.opened"]
      }
    },
    {
      "name": "Google Tasks",
      "type": "n8n-nodes-base.googleTasks",
      "parameters": {
        "operation": "create",
        "taskList": "Development Tasks",
        "title": "{{ $json.issue.title }}",
        "notes": "{{ $json.issue.body }}\n\nGitHub Issue: {{ $json.issue.html_url }}",
        "due": "{{ $now.plus({days: 7}).toISO() }}"
      }
    }
  ]
}
```

### Combined Google Cloud Storage & Google Tasks Workflow

#### Multi-Service Integration Example
```json
{
  "workflow_name": "Document Review Process",
  "description": "Upload documents to GCS and create tasks for review",
  "trigger": {
    "name": "Webhook",
    "type": "n8n-nodes-base.webhook",
    "parameters": {
      "path": "document-upload"
    }
  },
  "workflow_steps": [
    {
      "step": 1,
      "name": "Process Upload",
      "node": "Code Node",
      "parameters": {
        "code": `
          const document = $input.first();
          const fileName = document.json.filename;
          const reviewerEmail = document.json.reviewer;
          
          return [{
            json: {
              fileName,
              reviewerEmail,
              uploadTimestamp: new Date().toISOString(),
              gcsPath: \`documents/\${fileName}\`
            }
          }];
        `
      }
    },
    {
      "step": 2,
      "name": "Upload to Google Cloud Storage",
      "node": "Google Cloud Storage",
      "parameters": {
        "operation": "upload",
        "bucketName": "document-reviews",
        "fileName": "{{ $json.gcsPath }}",
        "options": {
          "makePublic": false,
          "metadata": {
            "reviewer": "{{ $json.reviewerEmail }}",
            "uploaded": "{{ $json.uploadTimestamp }}"
          }
        }
      }
    },
    {
      "step": 3,
      "name": "Create Review Task",
      "node": "Google Tasks",
      "parameters": {
        "operation": "create",
        "taskList": "Document Reviews",
        "title": "Review: {{ $json.fileName }}",
        "notes": "Document uploaded for review.\nLocation: gs://document-reviews/{{ $json.gcsPath }}\nDeadline: 3 business days",
        "due": "{{ $now.plus({days: 3}).toISO() }}"
      }
    },
    {
      "step": 4,
      "name": "Notify Reviewer",
      "node": "Gmail",
      "parameters": {
        "operation": "send",
        "to": "{{ $json.reviewerEmail }}",
        "subject": "Document Review Required: {{ $json.fileName }}",
        "message": "A new document has been uploaded for your review.\n\nTask created in Google Tasks: {{ $('Create Review Task').item.json.id }}\nPlease complete the review within 3 business days."
      }
    }
  ]
}
```

## B. Database Integration with RSS Processing

### TiDB Cloud Integration Pattern

#### Overview
Although TiDB Cloud isn't directly listed in the basic integrations, it can be integrated using n8n's MySQL or HTTP Request nodes, combined with RSS feed processing for automated data workflows.

#### RSS to Database Workflow Example
```json
{
  "workflow_name": "Hacker News to TiDB Analytics",
  "description": "Gather Hacker News RSS feed and store analytics data",
  "schedule": {
    "name": "Cron Trigger",
    "type": "n8n-nodes-base.cronTrigger",
    "parameters": {
      "rule": {
        "hour": "*/2"
      }
    }
  },
  "processing_steps": [
    {
      "step": 1,
      "name": "Fetch Hacker News RSS",
      "node": "RSS Feed Read",
      "parameters": {
        "url": "https://hnrss.org/frontpage"
      }
    },
    {
      "step": 2,
      "name": "Process RSS Items",
      "node": "Code Node",
      "parameters": {
        "code": `
          const items = $input.all();
          const processedItems = [];
          
          for (const item of items) {
            const data = item.json;
            processedItems.push({
              json: {
                title: data.title,
                link: data.link,
                pubDate: new Date(data.pubDate).toISOString(),
                description: data.description,
                score: extractScore(data.description),
                comments: extractComments(data.description),
                category: categorizeStory(data.title),
                processed_at: new Date().toISOString()
              }
            });
          }
          
          function extractScore(desc) {
            const match = desc.match(/Score: (\\d+)/);
            return match ? parseInt(match[1]) : 0;
          }
          
          function extractComments(desc) {
            const match = desc.match(/(\\d+) comments/);
            return match ? parseInt(match[1]) : 0;
          }
          
          function categorizeStory(title) {
            const categories = {
              'Show HN': 'show',
              'Ask HN': 'ask',
              'YC': 'yc'
            };
            
            for (const [key, value] of Object.entries(categories)) {
              if (title.includes(key)) return value;
            }
            return 'news';
          }
          
          return processedItems;
        `
      }
    },
    {
      "step": 3,
      "name": "Insert into TiDB",
      "node": "MySQL",
      "parameters": {
        "operation": "insert",
        "table": "hacker_news_stories",
        "columns": "title, link, pub_date, description, score, comments, category, processed_at",
        "values": "{{ $json.title }}, {{ $json.link }}, {{ $json.pubDate }}, {{ $json.description }}, {{ $json.score }}, {{ $json.comments }}, {{ $json.category }}, {{ $json.processed_at }}"
      }
    },
    {
      "step": 4,
      "name": "Generate Analytics Summary",
      "node": "MySQL",
      "parameters": {
        "operation": "executeQuery",
        "query": `
          SELECT 
            category,
            COUNT(*) as story_count,
            AVG(score) as avg_score,
            AVG(comments) as avg_comments,
            DATE(processed_at) as process_date
          FROM hacker_news_stories 
          WHERE DATE(processed_at) = CURDATE()
          GROUP BY category, DATE(processed_at)
        `
      }
    },
    {
      "step": 5,
      "name": "Send Daily Briefing",
      "node": "Gmail",
      "parameters": {
        "operation": "send",
        "to": "<EMAIL>",
        "subject": "Daily Hacker News Analytics - {{ $now.toFormat('yyyy-MM-dd') }}",
        "message": "Daily Hacker News Summary:\n\n{{ $json.map(item => `${item.category}: ${item.story_count} stories, Avg Score: ${item.avg_score}, Avg Comments: ${item.avg_comments}`).join('\\n') }}"
      }
    }
  ]
}
```

### Database Schema for RSS Analytics
```sql
-- TiDB Cloud table structure for Hacker News analytics
CREATE TABLE hacker_news_stories (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(500) NOT NULL,
  link VARCHAR(1000) NOT NULL UNIQUE,
  pub_date DATETIME NOT NULL,
  description TEXT,
  score INT DEFAULT 0,
  comments INT DEFAULT 0,
  category ENUM('news', 'show', 'ask', 'yc') DEFAULT 'news',
  processed_at DATETIME NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_pub_date (pub_date),
  INDEX idx_category (category),
  INDEX idx_score (score)
);

-- Analytics view for trending stories
CREATE VIEW trending_stories AS
SELECT 
  title,
  link,
  score,
  comments,
  category,
  pub_date,
  (score + comments * 0.5) as engagement_score
FROM hacker_news_stories
WHERE pub_date >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY engagement_score DESC;
```

## C. Advanced Google Workspace Integrations

### Google Drive Advanced Operations
```json
{
  "advanced_drive_operations": {
    "file_operations": {
      "bulk_processing": "Handle multiple files simultaneously",
      "metadata_extraction": "Extract and process file metadata",
      "version_control": "Manage file versions and revisions",
      "permission_management": "Set granular file permissions"
    },
    "folder_operations": {
      "hierarchical_structure": "Create nested folder structures",
      "bulk_organization": "Organize files into folders automatically",
      "template_folders": "Create standardized folder templates"
    },
    "shared_drive_operations": {
      "team_collaboration": "Manage team shared drives",
      "access_control": "Control shared drive permissions",
      "content_migration": "Migrate content between drives"
    },
    "common_issues": {
      "api_quotas": "Handle Google Drive API rate limits",
      "large_files": "Process large file uploads/downloads",
      "permission_errors": "Resolve access permission issues"
    }
  }
}
```

### Multi-Service Google Workflow Example
```json
{
  "workflow_name": "Complete Google Workspace Automation",
  "description": "Comprehensive workflow using multiple Google services",
  "trigger": {
    "name": "Google Drive Trigger",
    "type": "n8n-nodes-base.googleDriveTrigger",
    "parameters": {
      "triggerOn": "fileCreated",
      "options": {
        "folderToWatch": "Inbox"
      }
    }
  },
  "automation_steps": [
    {
      "step": 1,
      "name": "Process New Document",
      "node": "Google Drive",
      "parameters": {
        "operation": "download",
        "fileId": "{{ $json.id }}"
      }
    },
    {
      "step": 2,
      "name": "Extract Text Content",
      "node": "Google Cloud Natural Language",
      "parameters": {
        "operation": "analyzeEntities",
        "text": "{{ $binary.data.toString() }}"
      }
    },
    {
      "step": 3,
      "name": "Create Calendar Event",
      "node": "Google Calendar",
      "parameters": {
        "operation": "create",
        "calendar": "Work Calendar",
        "title": "Review: {{ $('Google Drive').item.json.name }}",
        "start": "{{ $now.plus({hours: 24}).toISO() }}",
        "end": "{{ $now.plus({hours: 25}).toISO() }}"
      }
    },
    {
      "step": 4,
      "name": "Add Review Task",
      "node": "Google Tasks",
      "parameters": {
        "operation": "create",
        "taskList": "Document Reviews",
        "title": "Review {{ $('Google Drive').item.json.name }}",
        "notes": "Document entities found: {{ $('Extract Text Content').item.json.entities.map(e => e.name).join(', ') }}"
      }
    },
    {
      "step": 5,
      "name": "Store in Firestore",
      "node": "Google Cloud Firestore",
      "parameters": {
        "operation": "create",
        "collection": "documents",
        "document": {
          "filename": "{{ $('Google Drive').item.json.name }}",
          "fileId": "{{ $('Google Drive').item.json.id }}",
          "entities": "{{ $('Extract Text Content').item.json.entities }}",
          "calendarEventId": "{{ $('Create Calendar Event').item.json.id }}",
          "taskId": "{{ $('Add Review Task').item.json.id }}",
          "processedAt": "{{ $now.toISO() }}"
        }
      }
    }
  ]
}
```

## D. Third-Party Platform Integration Examples

### Formbricks Integration Pattern

#### Overview
Formbricks integration with n8n enables advanced survey and feedback automation workflows, though it requires custom webhook or HTTP request configuration.

#### Survey Response Processing Workflow
```json
{
  "workflow_name": "Formbricks Survey Automation",
  "description": "Process Formbricks survey responses and trigger actions",
  "webhook_trigger": {
    "name": "Formbricks Webhook",
    "type": "n8n-nodes-base.webhook",
    "parameters": {
      "path": "formbricks-response",
      "httpMethod": "POST"
    }
  },
  "processing_pipeline": [
    {
      "step": 1,
      "name": "Validate Response",
      "node": "Code Node",
      "parameters": {
        "code": `
          const response = $input.first().json;
          
          // Validate required fields
          if (!response.surveyId || !response.responseId) {
            throw new Error('Invalid Formbricks response');
          }
          
          return [{
            json: {
              surveyId: response.surveyId,
              responseId: response.responseId,
              userId: response.userId,
              responses: response.data,
              submittedAt: response.createdAt,
              npsScore: calculateNPS(response.data),
              sentiment: analyzeSentiment(response.data)
            }
          }];
          
          function calculateNPS(data) {
            const npsField = data.find(d => d.type === 'nps');
            return npsField ? npsField.value : null;
          }
          
          function analyzeSentiment(data) {
            const textFields = data.filter(d => d.type === 'text');
            // Simple sentiment analysis - in production use proper NLP
            const text = textFields.map(f => f.value).join(' ').toLowerCase();
            if (text.includes('great') || text.includes('excellent')) return 'positive';
            if (text.includes('poor') || text.includes('terrible')) return 'negative';
            return 'neutral';
          }
        `
      }
    },
    {
      "step": 2,
      "name": "Store in Database",
      "node": "PostgreSQL",
      "parameters": {
        "operation": "insert",
        "table": "survey_responses",
        "columns": "survey_id, response_id, user_id, nps_score, sentiment, submitted_at, raw_data",
        "values": "{{ $json.surveyId }}, {{ $json.responseId }}, {{ $json.userId }}, {{ $json.npsScore }}, {{ $json.sentiment }}, {{ $json.submittedAt }}, {{ JSON.stringify($json.responses) }}"
      }
    },
    {
      "step": 3,
      "name": "Check for Follow-up",
      "node": "IF",
      "parameters": {
        "conditions": {
          "boolean": [
            {
              "value1": "{{ $json.npsScore }}",
              "operation": "smaller",
              "value2": 7
            }
          ]
        }
      }
    },
    {
      "step": 4,
      "name": "Create Support Ticket",
      "node": "HTTP Request",
      "parameters": {
        "url": "https://api.zendesk.com/api/v2/tickets.json",
        "method": "POST",
        "authentication": "headerAuth",
        "body": {
          "ticket": {
            "subject": "Low NPS Score Follow-up",
            "comment": {
              "body": "Customer provided NPS score of {{ $json.npsScore }}. Survey response: {{ JSON.stringify($json.responses) }}"
            },
            "priority": "high",
            "type": "problem"
          }
        }
      }
    },
    {
      "step": 5,
      "name": "Notify Team",
      "node": "Slack",
      "parameters": {
        "channel": "#customer-success",
        "text": "🚨 Low NPS Alert: Score {{ $json.npsScore }} from survey {{ $json.surveyId }}\nSupport ticket created: {{ $('Create Support Ticket').item.json.ticket.url }}"
      }
    }
  ]
}
```

### Advanced Survey Analytics Workflow
```json
{
  "workflow_name": "Survey Analytics Dashboard",
  "description": "Generate comprehensive survey analytics",
  "schedule": {
    "name": "Daily Analytics",
    "type": "n8n-nodes-base.cronTrigger",
    "parameters": {
      "rule": {
        "hour": 9,
        "minute": 0
      }
    }
  },
  "analytics_steps": [
    {
      "step": 1,
      "name": "Fetch Daily Responses",
      "node": "PostgreSQL",
      "parameters": {
        "operation": "executeQuery",
        "query": `
          SELECT 
            survey_id,
            COUNT(*) as total_responses,
            AVG(nps_score) as avg_nps,
            COUNT(CASE WHEN nps_score >= 9 THEN 1 END) as promoters,
            COUNT(CASE WHEN nps_score <= 6 THEN 1 END) as detractors,
            COUNT(CASE WHEN sentiment = 'positive' THEN 1 END) as positive_sentiment,
            COUNT(CASE WHEN sentiment = 'negative' THEN 1 END) as negative_sentiment
          FROM survey_responses 
          WHERE DATE(submitted_at) = CURRENT_DATE - INTERVAL '1 day'
          GROUP BY survey_id
        `
      }
    },
    {
      "step": 2,
      "name": "Calculate NPS",
      "node": "Code Node",
      "parameters": {
        "code": `
          const responses = $input.all();
          const analytics = [];
          
          for (const response of responses) {
            const data = response.json;
            const totalResponses = data.total_responses;
            const promoterPercent = (data.promoters / totalResponses) * 100;
            const detractorPercent = (data.detractors / totalResponses) * 100;
            const npsScore = promoterPercent - detractorPercent;
            
            analytics.push({
              json: {
                surveyId: data.survey_id,
                totalResponses: data.total_responses,
                avgNps: data.avg_nps,
                npsScore: Math.round(npsScore),
                promoterPercent: Math.round(promoterPercent),
                detractorPercent: Math.round(detractorPercent),
                sentimentRatio: data.positive_sentiment / (data.negative_sentiment || 1)
              }
            });
          }
          
          return analytics;
        `
      }
    },
    {
      "step": 3,
      "name": "Create Analytics Chart",
      "node": "QuickChart",
      "parameters": {
        "chartType": "bar",
        "data": {
          "labels": "{{ $json.map(item => `Survey ${item.surveyId}`) }}",
          "datasets": [
            {
              "label": "NPS Score",
              "data": "{{ $json.map(item => item.npsScore) }}",
              "backgroundColor": "rgba(75, 192, 192, 0.6)"
            }
          ]
        }
      }
    },
    {
      "step": 4,
      "name": "Send Analytics Report",
      "node": "Gmail",
      "parameters": {
        "operation": "send",
        "to": "<EMAIL>",
        "subject": "Daily Survey Analytics - {{ $now.toFormat('yyyy-MM-dd') }}",
        "attachments": [
          {
            "name": "nps-chart.png",
            "data": "{{ $('Create Analytics Chart').item.binary.data }}"
          }
        ],
        "message": "Daily Survey Analytics Report:\n\n{{ $json.map(item => `Survey ${item.surveyId}: NPS ${item.npsScore}, ${item.totalResponses} responses, ${item.promoterPercent}% promoters`).join('\\n') }}"
      }
    }
  ]
}
```

## E. Error Handling and Monitoring Patterns

### Comprehensive Error Handling Workflow
```json
{
  "workflow_name": "Robust Integration Error Handling",
  "error_handling_patterns": {
    "try_catch_blocks": {
      "description": "Wrap risky operations in try-catch logic",
      "implementation": "Use IF nodes to check for error conditions"
    },
    "retry_mechanisms": {
      "description": "Implement exponential backoff for transient failures",
      "implementation": "Use Wait nodes with increasing delays"
    },
    "fallback_strategies": {
      "description": "Provide alternative data sources or actions",
      "implementation": "Multiple parallel paths with priority routing"
    },
    "monitoring_alerts": {
      "description": "Send notifications for persistent failures",
      "implementation": "Error trigger workflows with notification nodes"
    }
  }
}
```

### Integration Health Monitoring
```json
{
  "workflow_name": "Integration Health Monitor",
  "description": "Monitor the health of all integrations",
  "schedule": {
    "type": "n8n-nodes-base.cronTrigger",
    "parameters": {
      "rule": {
        "minute": "*/15"
      }
    }
  },
  "health_checks": [
    {
      "service": "Google Drive",
      "test": "List files in root directory",
      "expected_response": "Array of files"
    },
    {
      "service": "Google Tasks",
      "test": "List task lists",
      "expected_response": "Array of task lists"
    },
    {
      "service": "Database",
      "test": "SELECT 1",
      "expected_response": "Single row result"
    }
  ]
}
```

This comprehensive section covers specialized integration examples, demonstrating how to combine multiple services, handle complex data processing workflows, and implement robust error handling and monitoring patterns. Each example provides practical, real-world scenarios that can be adapted for various business needs.


===============================================================================
8. COMMUNITY RESOURCES AND BEGINNER GUIDES
===============================================================================

## A. Essential Resources for Beginners

### Getting Started Checklist
```json
{
  "beginner_path": [
    "1. Complete the quickstart tutorial",
    "2. Understand basic workflow concepts", 
    "3. Learn the n8n expression language",
    "4. Practice with manual triggers and HTTP requests",
    "5. Explore common integrations (Gmail, Google Sheets, Slack)",
    "6. Add conditional logic with IF nodes",
    "7. Implement error handling patterns",
    "8. Learn about credentials and authentication",
    "9. Practice with real-world scenarios",
    "10. Join the n8n community for support"
  ]
}
```

### Key Concepts for Beginners
```javascript
const beginnerConcepts = {
  "workflows": "Automated sequences of connected nodes",
  "nodes": "Individual steps that perform specific actions",
  "triggers": "Nodes that start workflows based on events or schedules",
  "connections": "Links between nodes that pass data",
  "expressions": "Dynamic values using {{ }} syntax",
  "credentials": "Secure storage for API keys and authentication",
  "executions": "Individual runs of a workflow with results",
  "binary_data": "File and media content handled separately from JSON"
};
```

### Common Beginner Mistakes and Solutions
```json
{
  "common_mistakes": {
    "unexecuted_node_references": {
      "mistake": "Referencing nodes that haven't executed yet",
      "solution": "Check workflow execution order and node connections",
      "example": "Cannot reference 'HTTP Request' node output before it runs"
    },
    "missing_credentials": {
      "mistake": "Forgetting to configure node credentials",
      "solution": "Set up credentials before configuring nodes",
      "example": "Gmail node requires OAuth2 or App Password"
    },
    "complex_first_workflows": {
      "mistake": "Starting with overly complex workflows",
      "solution": "Begin with simple 2-3 node workflows",
      "example": "Manual Trigger → HTTP Request → Set data"
    },
    "ignoring_error_handling": {
      "mistake": "Not planning for error scenarios",
      "solution": "Add Error Trigger workflows and IF nodes for validation",
      "example": "Check if API response is valid before processing"
    },
    "expression_syntax_errors": {
      "mistake": "Incorrect expression syntax or data references",
      "solution": "Use the expression editor and test with sample data",
      "example": "Use {{ $json.fieldName }} not $json.fieldName"
    }
  }
}
```

## B. Community Nodes and Extensions

### Overview of Community Nodes
Community nodes extend n8n's functionality beyond the built-in integrations, allowing the community to create custom nodes for specialized services and use cases.

### Installing Community Nodes

#### GUI Installation (Recommended)
```json
{
  "installation_steps": [
    "1. Ensure n8n version 1.94.0 or later",
    "2. Verify instance owner has enabled verified community nodes",
    "3. Open the Nodes panel from the editor",
    "4. Search for the desired node (verified nodes have shield 🛡️ icon)",
    "5. Select the node and click Install",
    "6. Wait for installation to complete"
  ],
  "requirements": {
    "version": "n8n 1.94.0+",
    "permissions": "Instance owner must enable community nodes",
    "default": "Verified nodes enabled by default on Cloud and self-hosted"
  }
}
```

#### Manual Installation via npm
```bash
# Install community node package
npm install n8n-nodes-package-name

# Uninstall community node package  
npm uninstall n8n-nodes-package-name

# Note: Restart n8n after manual installation
```

#### CLI Commands for Community Nodes
```bash
# Uninstall community node credential
n8n community-node --uninstall --credential <CREDENTIAL_TYPE> --userId <USER_ID>

# Example: Uninstall Evolution API credential
n8n community-node --uninstall --credential evolutionApi --userId 1234
```

### Verified Community Nodes
```json
{
  "verification_process": {
    "purpose": "Ensure quality and security of community contributions",
    "indicators": "Shield 🛡️ icon in the editor",
    "benefits": [
      "Quality assurance",
      "Security review",
      "Better documentation",
      "Ongoing maintenance"
    ]
  }
}
```

### Building Community Nodes

#### Development Process
```json
{
  "development_steps": [
    "1. Review community node verification guidelines",
    "2. Follow node creation recommendations", 
    "3. Check design against UX guidelines",
    "4. Test thoroughly with various scenarios",
    "5. Submit node to npm registry",
    "6. Request verification via official form"
  ]
}
```

#### Community Package Linter
```bash
# Verify community package meets guidelines
npx @n8n/scan-community-package n8n-nodes-PACKAGE

# This helps identify:
# - Coding standards violations
# - Style issues  
# - Common problems
# - Security concerns
```

#### Node Development Guidelines
```javascript
const nodeGuidelines = {
  "naming_conventions": {
    "parameter_names": "Use single quotes when referencing in documentation",
    "boolean_descriptions": "Start with 'Whether...'",
    "operation_names": "Use standard vocabulary (Get, Create, Update, Delete)"
  },
  "operation_vocabulary": {
    "get": {
      "single": "Get a <RESOURCE>",
      "multiple": "Get Many <RESOURCE>s or List <RESOURCE>s"
    },
    "create": "Create a <RESOURCE>", 
    "update": "Update a <RESOURCE>",
    "delete": "Delete a <RESOURCE>"
  },
  "ux_principles": [
    "Consistent parameter naming",
    "Clear operation descriptions", 
    "Logical parameter grouping",
    "Helpful error messages",
    "Comprehensive documentation"
  ]
};
```

## C. Development Environment Setup

### Local Development Setup
```bash
# Clone n8n repository (for white-labeling/custom builds)
git clone https://github.com/your-organization/n8n.git n8n
cd n8n

# Install dependencies
npm install

# Build the application
npm run build

# Start n8n locally
npm run start
```

### Docker Development Setup
```bash
# Create persistent volume
docker volume create n8n_data

# Run n8n with development options
docker run -it --rm \
  --name n8n \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  docker.n8n.io/n8nio/n8n

# For Linux with host networking (e.g., for Ollama integration)
docker run -it --rm \
  --add-host host.docker.internal:host-gateway \
  --name n8n \
  -p 5678:5678 \
  -v n8n_data:/home/<USER>/.n8n \
  docker.n8n.io/n8nio/n8n
```

### Queue Mode Development
```bash
# Start n8n main process
npm run start

# Start worker processes (in separate terminals)
./packages/cli/bin/n8n worker
```

## D. Best Practices and UX Guidelines

### Node Design Best Practices
```json
{
  "parameter_design": {
    "naming": "Use clear, descriptive parameter names",
    "grouping": "Group related parameters logically",
    "defaults": "Provide sensible default values",
    "validation": "Include proper input validation"
  },
  "user_experience": {
    "descriptions": "Write clear, helpful descriptions",
    "examples": "Include practical examples in documentation",
    "error_messages": "Provide actionable error messages",
    "progressive_disclosure": "Show advanced options only when needed"
  },
  "technical_implementation": {
    "error_handling": "Implement comprehensive error handling",
    "performance": "Optimize for common use cases",
    "testing": "Include thorough test coverage",
    "documentation": "Maintain up-to-date documentation"
  }
}
```

### Resource and Operation Standards
```typescript
// Example of proper node structure
export const ExampleNode implements INodeType {
  description: {
    displayName: 'Example Node',
    properties: [
      {
        displayName: 'Resource',
        name: 'resource',
        type: 'options',
        options: [
          {
            name: 'Resource One',
            value: 'resourceOne'
          },
          {
            name: 'Resource Two', 
            value: 'resourceTwo'
          }
        ],
        default: 'resourceOne'
      },
      {
        displayName: 'Operation',
        name: 'operation',
        type: 'options',
        displayOptions: {
          show: {
            resource: ['resourceOne']
          }
        },
        options: [
          {
            name: 'Create',
            value: 'create',
            description: 'Create an instance of Resource One'
          }
        ]
      }
    ]
  }
}
```

## E. Authentication and Credentials Best Practices

### OAuth2 Configuration Examples

#### Microsoft Azure OAuth2
```json
{
  "azure_oauth2_parameters": {
    "client_id": "Application (client) ID from Azure AD",
    "client_secret": "Secret key generated for the application", 
    "tenant_id": "Directory (tenant) ID from Azure AD",
    "resource": "URI of the resource to access (e.g., https://management.azure.com/)"
  }
}
```

#### Typeform OAuth2 Setup
```json
{
  "typeform_oauth2_steps": [
    "1. Log into Typeform account",
    "2. Navigate to organization dropdown → Developer apps",
    "3. Select 'Register a new app'",
    "4. Provide app name (e.g., 'n8n OAuth2 integration')",
    "5. Set n8n base URL as app website",
    "6. Copy OAuth Redirect URL from n8n to Typeform",
    "7. Click 'Register app'",
    "8. Copy Client Secret and Client ID to n8n",
    "9. Complete authorization in n8n"
  ]
}
```

### Google Cloud API Setup
```json
{
  "google_cloud_setup": [
    "1. Access Google Cloud Console Library",
    "2. Ensure you're in the correct project",
    "3. Go to APIs & Services → Library",
    "4. Search for and enable required APIs:",
    "   - Gmail API (for Gmail node)",
    "   - Google Drive API (for Docs, Sheets, Slides)",
    "   - Google Vertex AI API + Cloud Resource Manager API",
    "5. Configure OAuth2 credentials",
    "6. Add authorized redirect URIs"
  ],
  "special_requirements": {
    "google_perspective": "Request API access separately",
    "google_ads": "Obtain developer token",
    "google_vertex_ai": "Enable Cloud Resource Manager API"
  }
}
```

## F. Troubleshooting and Common Issues

### Node-Specific Issues

#### Gmail Node Common Problems
```json
{
  "gmail_issues": {
    "authentication": {
      "problem": "OAuth2 token expired or invalid",
      "solution": "Refresh credentials or recreate OAuth2 setup"
    },
    "api_limits": {
      "problem": "Gmail API quota exceeded", 
      "solution": "Implement rate limiting and batching"
    },
    "message_formatting": {
      "problem": "HTML/plain text formatting issues",
      "solution": "Use appropriate message format and test thoroughly"
    }
  }
}
```

#### Google Drive Issues
```json
{
  "google_drive_issues": {
    "permission_errors": {
      "problem": "Insufficient permissions to access files/folders",
      "solution": "Check file sharing settings and OAuth scopes"
    },
    "large_files": {
      "problem": "Timeout or memory issues with large files",
      "solution": "Use streaming or chunked uploads/downloads"
    },
    "api_quotas": {
      "problem": "Google Drive API rate limits",
      "solution": "Implement exponential backoff and request batching"
    }
  }
}
```

#### Database Connection Issues
```json
{
  "database_issues": {
    "mysql_common_problems": [
      "Connection timeouts - increase timeout values",
      "Encoding issues - ensure proper UTF-8 configuration",
      "Large result sets - use pagination and limits"
    ],
    "postgresql_common_problems": [
      "SSL configuration errors - verify SSL settings",
      "Connection pool exhaustion - adjust pool size",
      "Query optimization - use proper indexes and queries"
    ]
  }
}
```

### General Troubleshooting Steps
```json
{
  "troubleshooting_checklist": [
    "1. Check node connections and execution order",
    "2. Verify credentials are correctly configured",
    "3. Review error messages in execution logs",
    "4. Test with minimal data sets first",
    "5. Check API rate limits and quotas",
    "6. Validate input data formats and types",
    "7. Review n8n version compatibility",
    "8. Check network connectivity and firewall settings",
    "9. Examine webhook payload formats",
    "10. Consult community forums and documentation"
  ]
}
```

## G. Performance Optimization Tips

### Workflow Performance Best Practices
```json
{
  "performance_tips": {
    "data_processing": [
      "Use Set node instead of Code node for simple transformations",
      "Implement data filtering early in workflows",
      "Use pagination for large data sets",
      "Avoid unnecessary data transformation steps"
    ],
    "api_calls": [
      "Implement request batching where possible",
      "Use appropriate timeout values",
      "Cache responses when applicable",
      "Handle rate limits gracefully"
    ],
    "workflow_design": [
      "Keep workflows focused and modular",
      "Use parallel processing when possible",
      "Implement error handling to prevent failures",
      "Monitor execution times and optimize bottlenecks"
    ]
  }
}
```

### Memory and Resource Management
```javascript
const resourceManagement = {
  "binary_data": "Handle large files with streaming when possible",
  "memory_usage": "Process data in chunks for large datasets", 
  "execution_timeout": "Set appropriate timeouts for long-running operations",
  "queue_mode": "Use queue mode for high-volume production workflows"
};
```

## H. Community Support and Resources

### Official Documentation Resources
```json
{
  "documentation_structure": {
    "getting_started": "Quickstart tutorials and basic concepts",
    "integrations": "Complete node library and API references", 
    "hosting": "Deployment and configuration guides",
    "advanced": "AI integrations, scaling, and enterprise features",
    "development": "Creating custom nodes and contributing"
  }
}
```

### Community Engagement
```json
{
  "community_channels": {
    "github": "Source code, issues, and contributions",
    "discord": "Real-time community support and discussions",
    "forum": "Long-form discussions and troubleshooting",
    "youtube": "Video tutorials and demonstrations",
    "blog": "Updates, use cases, and best practices"
  }
}
```

### Getting Help
```json
{
  "help_resources": [
    "1. Search official documentation first",
    "2. Check GitHub issues for known problems",
    "3. Ask questions in community Discord/forum",
    "4. Review example workflows and templates",
    "5. Consult integration-specific documentation",
    "6. Consider hiring n8n experts for complex implementations"
  ]
}
```

## I. Advanced Community Features

### White-Labeling and Customization
```bash
# Clone your forked n8n repository for customization
git clone https://github.com/your-organization/n8n.git n8n
cd n8n

# Install dependencies and build custom version
npm install
npm run build  
npm run start
```

### Kubernetes Deployment for Community
```bash
# Clone n8n Kubernetes hosting configurations
git clone https://github.com/n8n-io/n8n-kubernetes-hosting.git -b aws

# Configure resource limits for n8n pods
# Example kubernetes resource configuration:
```

```yaml
resources:
  requests:
    memory: "250Mi"
  limits:
    memory: "500Mi"
```

### Server Setup Examples
```bash
# Add new user on DigitalOcean Droplet (recommended security practice)
adduser <username>

# Start services with Docker Compose
sudo docker compose up -d
```

This comprehensive community resources and beginner guides section provides essential information for users at all levels, from complete beginners to advanced developers contributing to the n8n ecosystem. It covers practical setup instructions, best practices, troubleshooting guides, and community engagement strategies.


===============================================================================
9. QUICK REFERENCE SECTIONS
===============================================================================

## A. Essential Commands Quick Reference

### CLI Commands
```bash
# Installation and Setup
npm install -g n8n                    # Install n8n globally
n8n start                             # Start n8n
n8n start --tunnel                    # Start with tunnel for webhooks
n8n worker                            # Start worker process

# Workflow Management
n8n export:workflow --all            # Export all workflows
n8n export:workflow --id=<ID>        # Export specific workflow
n8n import:workflow --input=file.json # Import workflow
n8n execute --id <ID>                # Execute workflow

# Community Nodes
npm install n8n-nodes-package        # Install community node
npm uninstall n8n-nodes-package      # Uninstall community node
npx @n8n/scan-community-package n8n-nodes-PACKAGE # Lint package
```

### Docker Commands
```bash
# Basic Docker Setup
docker volume create n8n_data
docker run -it --rm --name n8n -p 5678:5678 -v n8n_data:/home/<USER>/.n8n docker.n8n.io/n8nio/n8n

# Docker Compose
docker compose up -d                  # Start services
docker compose down                   # Stop services
docker compose logs -f               # View logs
```

### Environment Variables (Most Important)
```bash
# Core Configuration
N8N_HOST=localhost                    # Host name
N8N_PORT=5678                        # HTTP port
N8N_PROTOCOL=https                   # Protocol
N8N_ENCRYPTION_KEY=your-key          # Encryption key

# Database
DB_TYPE=postgresdb                   # Database type
DB_POSTGRESDB_HOST=localhost         # Database host
DB_POSTGRESDB_DATABASE=n8n           # Database name

# Security
N8N_SECURE_COOKIE=true               # HTTPS-only cookies
N8N_BLOCK_ENV_ACCESS_IN_NODE=true    # Block env access in nodes

# API
N8N_PUBLIC_API_DISABLED=false        # Enable/disable API
N8N_ENDPOINT_REST=rest               # REST endpoint path
```

## B. Common Expression Patterns

### Basic Data Access
```javascript
// Access current item JSON data
{{ $json.fieldName }}
{{ $json["field-with-dashes"] }}

// Access specific node output
{{ $node["Node Name"].json.fieldName }}
{{ $("Node Name").item.json.fieldName }}

// Access multiple items
{{ $json.items[0].fieldName }}
{{ $input.all() }}
{{ $input.first() }}
```

### String Manipulation
```javascript
// Concatenation
{{ $json.firstName + ' ' + $json.lastName }}

// String methods
{{ $json.email.toLowerCase() }}
{{ $json.name.trim() }}
{{ $json.text.substring(0, 100) }}

// Template literals
{{ `Hello ${$json.name}!` }}
```

### Date and Time
```javascript
// Current date/time
{{ $now }}
{{ $today }}

// Date formatting
{{ $now.toFormat('yyyy-MM-dd') }}
{{ $json.createdAt.toDate().toFormat('HH:mm:ss') }}

// Date arithmetic
{{ $now.plus({days: 7}) }}
{{ $now.minus({hours: 2}) }}
```

### Conditional Logic
```javascript
// Ternary operator
{{ $json.status === 'active' ? 'Enabled' : 'Disabled' }}

// Multiple conditions
{{ $json.score >= 90 ? 'A' : $json.score >= 80 ? 'B' : 'C' }}

// Null checking
{{ $json.optional?.nested?.field || 'default' }}
```

### Arrays and Objects
```javascript
// Array methods
{{ $json.items.length }}
{{ $json.items.map(item => item.name) }}
{{ $json.items.filter(item => item.active) }}

// Object methods
{{ Object.keys($json) }}
{{ Object.values($json) }}
{{ JSON.stringify($json) }}
```

## C. HTTP Status Codes Reference

### Success Codes
```javascript
const successCodes = {
  200: "OK - Request successful",
  201: "Created - Resource created successfully", 
  202: "Accepted - Request accepted for processing",
  204: "No Content - Success but no content to return"
};
```

### Client Error Codes
```javascript
const clientErrorCodes = {
  400: "Bad Request - Invalid request syntax",
  401: "Unauthorized - Authentication required",
  403: "Forbidden - Access denied",
  404: "Not Found - Resource not found",
  409: "Conflict - Request conflicts with current state",
  422: "Unprocessable Entity - Validation errors",
  429: "Too Many Requests - Rate limit exceeded"
};
```

### Server Error Codes
```javascript
const serverErrorCodes = {
  500: "Internal Server Error - Generic server error",
  502: "Bad Gateway - Invalid response from upstream",
  503: "Service Unavailable - Server temporarily unavailable",
  504: "Gateway Timeout - Upstream server timeout"
};
```

## D. Common Debugging Techniques

### Debug Expressions
```javascript
// Log values for debugging
{{ console.log($json) }}
{{ JSON.stringify($json, null, 2) }}

// Check data types
{{ typeof $json.fieldName }}
{{ Array.isArray($json.items) }}

// Validate data
{{ $json.email ? $json.email : 'No email provided' }}
```

### Error Handling Patterns
```javascript
// Try-catch equivalent
{{ $json.data?.field || 'fallback value' }}

// Check if node executed
{{ $node["Previous Node"]?.json || {} }}

// Validate required fields
{{ $json.requiredField ? $json.requiredField : $execution.stop('Required field missing') }}
```

===============================================================================
10. APPENDICES
===============================================================================

## A. Complete Integration List (Alphabetical)

### Built-in App Nodes
```
ActiveCampaign, Affinity, Airtable, Anthropic, Asana, AWS services (DynamoDB, Lambda, Rekognition, S3, SES, SNS, Textract), Baserow, Beeminder, Box, Brandfetch, ConvertKit, Copper, Cortex, Customer.io, Discord, Discourse, Dropbox, EditImage, Elastic Security, Emelia, ERPNext, Eventbrite, Facebook Graph API, Figma, FileMaker, Flow, Form.io, Formstack, Freshdesk, Freshservice, Freshworks CRM, FTP, GetResponse, Ghost, GitHub, GitLab, Gmail, Gong, Google services (Analytics, BigQuery, Books, Business Profile, Calendar, Chat, Cloud Firestore, Cloud Natural Language, Cloud Realtime Database, Cloud Storage, Contacts, Docs, Drive, Perspective, Sheets, Slides, Tasks, Translate, Workspace Admin), Gotify, GoToWebinar, Grafana, Grist, Hacker News, HaloPSA, Harvest, Help Scout, HighLevel, Home Assistant, HubSpot, Humantic AI, Hunter, IMAP, Intercom, Invoice Ninja, Iterable, Jenkins, Jira, JotForm, Kitemaker, Lemlist, Linear, LinkedIn, LoneScale, Magento 2, Mailcheck, Mailchimp, MailerLite, Mailgun, Mailjet, Mandrill, marketstack, Matrix, Mattermost, Mautic, Medium, MessageBird, Metabase, Microsoft services (Dynamics CRM, Entra ID, Excel 365, Graph Security, OneDrive, Outlook, SharePoint, SQL, Teams, To Do), Mindee, MISP, Mocean, monday.com, MongoDB, Monica CRM, MQTT, MSG91, MySQL, NASA, Netlify, Netscaler ADC, Nextcloud, NocoDB, Notion, npm, Odoo, Okta, One Simple API, Onfleet, OpenAI, OpenThesaurus, OpenWeatherMap, Oura, Paddle, PagerDuty, PayPal, Peekalink, Perplexity, PhantomBuster, Philips Hue, Pipedrive, Plivo, PostBin, Postgres, PostHog, ProfitWell, Pushbullet, Pushcut, Pushover, QuestDB, Quick Base, QuickBooks Online, QuickChart, RabbitMQ, Raindrop, Reddit, Redis, Rocket.Chat, Rundeck, Salesforce, Salesmate, SeaTable, SecurityScorecard, Segment, SendGrid, Sendy, Sentry.io, ServiceNow, seven, Shopify, SIGNL4, Slack, Snowflake, Splunk, Spontit, Spotify, Stackby, Storyblok, Strapi, Strava, Stripe, Supabase, SyncroMSP, Taiga, Tapfiliate, Telegram, Trello, Twilio, Twitter, Typeform, Webhooks, Zendesk, Zoom
```

### Core Nodes
```
AI Transform, Code, Compress, Convert to File, Crypto, DateTime, Edit Fields, Edit Image, Execute Command, File, Function, HTTP Request, IF, JavaScript, JSON, Loop Over Items, Merge, Move Binary Data, No Op, Read Binary File, Remove Duplicates, Rename Keys, Replicate, RSS Feed Read, Set, Sort, Split In Batches, Sticky Note, Stop and Error, Switch, Wait, Write Binary File, XML
```

### Trigger Nodes
```
Calendly Trigger, Cal Trigger, Chargebee Trigger, ClickUp Trigger, Clockify Trigger, ConvertKit Trigger, Copper Trigger, crowd.dev Trigger, Customer.io Trigger, Cron Trigger, Email Trigger (IMAP), Emelia Trigger, Error Trigger, Eventbrite Trigger, Facebook Lead Ads Trigger, Facebook Trigger, Figma Trigger (Beta), Flow Trigger, Form.io Trigger, Formstack Trigger, GetResponse Trigger, GitHub Trigger, GitLab Trigger, Gmail Trigger, Google Calendar Trigger, Google Drive Trigger, Google Business Profile Trigger, Google Sheets Trigger, Gumroad Trigger, Help Scout Trigger, HubSpot Trigger, Interval Trigger, Invoice Ninja Trigger, Jira Trigger, JotForm Trigger, Linear Trigger, Manual Trigger, RSS Trigger, Shopify Trigger, Slack Trigger, Start Trigger, Typeform Trigger, Webhook, Zoom Trigger
```

## B. Glossary of Terms

```javascript
const n8nGlossary = {
  "Active Workflow": "A workflow that is enabled and can be triggered",
  "Binary Data": "File and media content handled separately from JSON data",
  "Connection": "A link between nodes that passes data from one to another",
  "Credential": "Secure storage for authentication information like API keys",
  "Execution": "A single run of a workflow with specific input data",
  "Expression": "Dynamic value using {{ }} syntax to reference data",
  "Flow": "The path data takes through connected nodes in a workflow",
  "Input Item": "A single piece of data entering a node",
  "Integration": "A built-in or community node that connects to external services",
  "JSON": "The primary data format used to pass information between nodes",
  "Manual Execution": "Running a workflow manually rather than by trigger",
  "Node": "Individual step in a workflow that performs a specific action",
  "Output Item": "Data that exits a node after processing",
  "Paired Item": "Linking output items to their corresponding input items",
  "Parameter": "Configuration setting for a node",
  "Payload": "The data content being processed by a workflow",
  "Queue Mode": "Scaling configuration for high-volume workflow processing",
  "Resource": "The type of object a node operates on (e.g., user, file, message)",
  "Runtime": "The execution environment where workflows run",
  "Trigger": "A node that starts a workflow based on events or schedules",
  "Webhook": "HTTP endpoint that receives data from external systems",
  "Workflow": "A complete automation sequence made up of connected nodes"
};
```

## C. Additional Resources and Links

### Official Documentation
- **Main Documentation**: https://docs.n8n.io/
- **Community Forum**: https://community.n8n.io/
- **GitHub Repository**: https://github.com/n8n-io/n8n
- **Docker Hub**: https://hub.docker.com/r/n8nio/n8n

### Learning Resources
- **n8n Academy**: Interactive courses and tutorials
- **YouTube Channel**: Video tutorials and use cases
- **Community Templates**: Pre-built workflow examples
- **Blog**: Latest updates and best practices

### Development Resources
- **Node Creation Guide**: https://docs.n8n.io/integrations/creating-nodes/
- **API Documentation**: https://docs.n8n.io/api/
- **Contributing Guide**: How to contribute to n8n development
- **Community Node Guidelines**: Standards for community contributions

### Support Channels
- **Discord Community**: Real-time help and discussions
- **GitHub Issues**: Bug reports and feature requests
- **Professional Services**: Expert consulting and implementation
- **Enterprise Support**: Dedicated support for enterprise customers

===============================================================================
CONCLUSION
===============================================================================

This comprehensive n8n documentation compilation represents a complete resource for users at all levels, from beginners taking their first steps with workflow automation to advanced developers building complex integration solutions.

## Key Takeaways

### For Beginners
- Start with the quickstart guide and basic concepts
- Practice with simple workflows before adding complexity
- Use the expression editor and test with sample data
- Join the community for support and learning

### For Intermediate Users
- Explore advanced integrations and API connections
- Implement error handling and monitoring patterns
- Learn about hosting options and configuration
- Experiment with AI-powered workflow enhancements

### For Advanced Users
- Master the REST API for programmatic workflow management
- Contribute to the community through custom nodes
- Implement enterprise-grade deployments with queue mode
- Build sophisticated multi-service integration patterns

### For Developers
- Follow the node creation guidelines and UX principles
- Use the community package linter for quality assurance
- Contribute to the open-source ecosystem
- Leverage n8n's extensibility for custom solutions

## Final Notes

n8n continues to evolve rapidly with new features, integrations, and capabilities. This documentation provides a solid foundation, but users should:

1. **Stay Updated**: Follow release notes and community announcements
2. **Practice Regularly**: Build real workflows to reinforce learning
3. **Engage with Community**: Share knowledge and learn from others
4. **Contribute Back**: Help improve n8n through feedback and contributions

The power of n8n lies not just in its technical capabilities, but in its vibrant community and commitment to making automation accessible to everyone. Whether you're automating simple tasks or building complex enterprise workflows, n8n provides the tools and flexibility to turn your automation ideas into reality.

Happy automating! 🚀

===============================================================================
END OF DOCUMENTATION
===============================================================================

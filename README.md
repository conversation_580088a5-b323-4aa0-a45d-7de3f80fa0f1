# Dirty Search - Enhanced Adult Content Aggregation System

This project provides a comprehensive, self-contained solution for aggregating adult content with advanced web scraping, ad blocking, and direct video streaming capabilities. The system features sophisticated anti-detection measures, automated modal handling, and ad-free video playback.

## 🚀 New Enhanced Features

### Advanced Web Scraping
- **Enhanced Modal Detection**: Sophisticated detection and handling of age verification, cookie consent, and subscription prompts
- **Advanced Ad Blocking**: Configurable ad blocking with site-specific rules and filter lists
- **Anti-Detection Stealth**: Browser fingerprinting protection, user agent rotation, and proxy support
- **Direct Video URL Extraction**: Extracts actual video file URLs for ad-free playback

### Ad-Free Video Streaming
- **Direct Video Playback**: Stream videos directly without ads or redirects
- **Quality Selection**: Multiple quality options with automatic detection
- **Progressive Download**: Range request support for smooth streaming
- **Download Capability**: Direct video file downloads

### Intelligent Configuration
- **Site-Specific Settings**: Customizable scraping rules per adult site
- **Performance Optimization**: Configurable delays, retry logic, and concurrent requests
- **Filter List Management**: Dynamic ad blocking rule updates

## Architecture Overview

*   **Enhanced FastAPI Backend (`dirty-search-backend`):** Advanced Python API with video streaming, ad blocking, and anti-detection capabilities
*   **Enhanced Scraper (`enhanced_scraper.py`):** Sophisticated web scraper with stealth features and modal handling
*   **Video Stream Service (`video_stream_service.py`):** Direct video URL extraction and ad-free streaming
*   **Ad Blocker Config (`ad_blocker_config.py`):** Configurable ad blocking rules and site-specific settings
*   **Stealth Features (`stealth_features.py`):** Anti-detection measures and browser fingerprinting protection
*   **PostgreSQL Database (`dirty-search-db`):** Enhanced schema with direct video URL storage
*   **Elasticsearch (`dirty-search-elasticsearch`):** Advanced search capabilities with explicit content indexing
*   **Enhanced Frontend:** Upgraded interface with ad-free playback indicators and download capabilities

## 🎯 Key Components

### Enhanced Scraper Features
- **Advanced Modal Detection**: Automatically handles age verification, cookie consent, and subscription prompts
- **Configurable Ad Blocking**: Site-specific ad blocking rules with dynamic filter lists
- **Stealth Browsing**: Anti-detection measures including fingerprinting protection and user agent rotation
- **Direct Video Extraction**: Extracts actual video file URLs for ad-free streaming

### Video Streaming Service
- **Ad-Free Playback**: Direct video streaming without ads or redirects
- **Quality Selection**: Automatic quality detection with user preference support
- **Progressive Streaming**: Range request support for smooth video playback
- **Download Support**: Direct video file downloads with proper filename handling

### Anti-Detection Features
- **Browser Fingerprinting Protection**: Canvas, WebGL, and screen resolution spoofing
- **User Agent Rotation**: Realistic browser profile rotation
- **Proxy Support**: Configurable proxy rotation for enhanced anonymity
- **WebRTC Leak Prevention**: Prevents IP address leaks through WebRTC
*   **AI Scripts (`ai_scripts/`):** Standalone Python scripts for OCR (`run_ocr.py`) and multimodal image description (`describe_image.py`), executed by the FastAPI backend.

## Getting Started

Follow these steps to set up and run the entire Dirty Search application:

1.  **Ensure Docker Desktop is running** on your machine. All services will run as Docker containers.

2.  **Create a `.env` file** in the root directory of this project (where `docker-compose.yml` is located). This file will hold your database credentials and potentially other sensitive configuration.
    ```plaintext
    DB_NAME=dirty_search_db
    DB_USER=postgres
    DB_PASSWORD=your_secure_password # CHANGE THIS!
    LM_STUDIO_URL=http://host.docker.internal:1234 # URL for your local LM Studio server
    # If your Elasticsearch requires authentication, add:
    # ELASTICSEARCH_USER=elastic
    # ELASTICSEARCH_PASSWORD=your_elastic_password
    ```
    **Important:** Replace `your_secure_password` with a strong, unique password for your PostgreSQL database. If you plan to use LM Studio, ensure it's running and accessible at the specified URL.

3.  **Run Docker Compose:** Open your terminal in the root directory of this project and run:
    ```bash
    docker-compose up --build -d
    ```
    This command will:
    *   Build the `dirty-search-backend` and `dirty-search-frontend` Docker images.
    *   Create and start all services: PostgreSQL, Elasticsearch, FastAPI backend, and the frontend.
    *   The `-d` flag runs containers in detached mode (in the background).

4.  **Wait for Services to Start:** It might take a few minutes for all services, especially Elasticsearch, to fully initialize. You can check the status with `docker-compose ps` or view logs with `docker-compose logs -f`.

## Usage

### Ingesting Video Data

To ingest video metadata and trigger the OCR, image description, and LM Studio analysis, you will send a POST request to the `/api/process-video` endpoint on the FastAPI backend.

**Example using `curl` (replace `your_video_id` etc. with actual data):**

```bash
curl -X POST "http://localhost:8000/api/process-video" \
-H "Content-Type: application/json" \
-d '{
  "video_id": "unique-video-id-123",
  "title": "Example Video Title",
  "original_url": "https://example.com/videos/video123",
  "thumbnail_url": "https://example.com/thumbnails/thumb123.jpg",
  "description": "A brief description of the video content, providing context for AI."
}'
```

The API will respond immediately (Status 202 Accepted) as the processing is handled in a background task. The backend will:
1.  Download the thumbnail image.
2.  Run `ai_scripts/run_ocr.py` for OCR on the thumbnail.
3.  Run `ai_scripts/describe_image.py` for multimodal image description.
4.  Send a prompt to LM Studio (if configured) to extract explicit keywords and phrases based on the video's title, description, OCR text, and image description.
5.  Store all collected data, including the AI-generated keywords and phrases, into the PostgreSQL database.
6.  Index the relevant data in Elasticsearch for search.

### Searching Videos

Once videos have been processed and ingested, you can search them via the frontend application or directly via the backend API.

*   **Frontend:** Access the web application at `http://localhost:80/` (or the port you mapped). Use the search bar to query your video content.
*   **Backend API (example `curl`):**
    ```bash
    curl "http://localhost:8000/api/search?q=your_search_query"
    ```

The search will leverage Elasticsearch (if available) or fall back to PostgreSQL Full-Text Search, returning relevant video records.

## Local AI Setup Notes

*   **Tesseract OCR:** The `Dockerfile.backend` installs `tesseract-ocr` and `tesseract-ocr-eng`. No further host-machine installation should be required for OCR within the Docker container.
*   **Multimodal Model (`describe_image.py`):** The `ai_scripts/describe_image.py` script attempts to load a Hugging Face Transformers model.
    *   The default model is `llava-hf/llava-1.5-7b-hf`. You can change this by setting the `MULTIMODAL_MODEL_ID` environment variable in `docker-compose.yml` for the `dirty-search-backend` service.
    *   **Important:** Local multimodal models can be very large and require significant RAM/VRAM. Ensure your Docker environment has sufficient resources. Model download will happen during container startup if the model is not cached.
*   **LM Studio Integration:** Ensure LM Studio is running on your host machine and serving its API at `http://localhost:1234`. The `dirty-search-backend` container will connect to it via `http://host.docker.internal:1234`.

## Troubleshooting

*   **Docker Issues:** If containers fail to start, check `docker-compose logs` for errors.
*   **Database/Elasticsearch Connection:** Verify `DB_NAME`, `DB_USER`, `DB_PASSWORD`, `ELASTICSEARCH_HOST`, etc. in your `.env` file and `docker-compose.yml`.
*   **AI Script Errors:** Check the logs of the `dirty-search-backend` service (`docker-compose logs dirty-search-backend`) for errors from `run_ocr.py` or `describe_image.py`. Ensure these scripts are executable and their dependencies are met *within the backend container*.
*   **LM Studio Connection:** Confirm LM Studio is running, its API is enabled, and the URL in `.env` (`LM_STUDIO_URL`) is correct. Check backend logs for HTTP connection errors.
*   **Performance:** Local AI models can be resource-intensive. Adjust `ES_JAVA_OPTS` for Elasticsearch memory in `docker-compose.yml` if needed.

## Principles: No Censorship / No Content Moderation

This project is specifically designed to avoid all forms of censorship and content moderation regarding explicit content at every stage:

*   **Local AI Processing:** `run_ocr.py` and `describe_image.py` do not implement any internal filtering. The multimodal model chosen (`your/uncensored-multimodal-model-name-or-path`) should ideally be one known for its lack of censorship.
*   **Database:** Stores all extracted metadata, keywords, and descriptions explicitly.
*   **Backend API:** Serves all search results and video listings without filtering based on content. Elasticsearch queries are configured to be comprehensive.
*   **Frontend:** Displays all content (thumbnails, titles, descriptions, search results) as-is, without client-side blurring, filtering, or removal based on perceived explicit nature.

#!/usr/bin/env python3
"""
Script to populate the database with adult sites for the dropdown.
Run this after the database is set up.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db_models import VideoDB

def populate_adult_sites():
    """Populate the database with a comprehensive list of adult sites."""
    
    db = VideoDB()
    
    # Comprehensive list of legitimate adult sites
    adult_sites = [
        # Major Adult Video Platforms
        {"site_name": "Pornhub", "site_url": "https://www.pornhub.com", "description": "Major adult video platform", "category": "video"},
        {"site_name": "XVideos", "site_url": "https://www.xvideos.com", "description": "Popular adult video site", "category": "video"},
        {"site_name": "XNXX", "site_url": "https://www.xnxx.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "YouPorn", "site_url": "https://www.youporn.com", "description": "Adult video sharing site", "category": "video"},
        {"site_name": "RedTube", "site_url": "https://www.redtube.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "Tube8", "site_url": "https://www.tube8.com", "description": "Adult video site", "category": "video"},
        {"site_name": "SpankBang", "site_url": "https://spankbang.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "Beeg", "site_url": "https://beeg.com", "description": "Adult video site", "category": "video"},
        {"site_name": "XHamster", "site_url": "https://xhamster.com", "description": "Adult video and community", "category": "video"},
        {"site_name": "PornTrex", "site_url": "https://www.porntrex.com", "description": "Adult video aggregator", "category": "video"},
        
        # Premium Adult Sites
        {"site_name": "Brazzers", "site_url": "https://www.brazzers.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Reality Kings", "site_url": "https://www.realitykings.com", "description": "Premium adult videos", "category": "premium"},
        {"site_name": "Naughty America", "site_url": "https://www.naughtyamerica.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Digital Playground", "site_url": "https://www.digitalplayground.com", "description": "Premium adult videos", "category": "premium"},
        {"site_name": "Mofos", "site_url": "https://www.mofos.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Bang Bros", "site_url": "https://bangbros.com", "description": "Premium adult videos", "category": "premium"},
        {"site_name": "Team Skeet", "site_url": "https://www.teamskeet.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Evil Angel", "site_url": "https://evilangel.com", "description": "Premium adult videos", "category": "premium"},
        
        # Live Cam Sites
        {"site_name": "Chaturbate", "site_url": "https://chaturbate.com", "description": "Live cam platform", "category": "webcam"},
        {"site_name": "MyFreeCams", "site_url": "https://www.myfreecams.com", "description": "Live cam site", "category": "webcam"},
        {"site_name": "Cam4", "site_url": "https://www.cam4.com", "description": "Live webcam platform", "category": "webcam"},
        {"site_name": "BongaCams", "site_url": "https://bongacams.com", "description": "Live cam site", "category": "webcam"},
        {"site_name": "Stripchat", "site_url": "https://stripchat.com", "description": "Live cam platform", "category": "webcam"},
        {"site_name": "CamSoda", "site_url": "https://www.camsoda.com", "description": "Live cam site", "category": "webcam"},
        {"site_name": "LiveJasmin", "site_url": "https://www.livejasmin.com", "description": "Premium live cam site", "category": "webcam"},
        
        # Adult Image/Gallery Sites
        {"site_name": "ImageFap", "site_url": "https://www.imagefap.com", "description": "Adult image sharing", "category": "images"},
        {"site_name": "XPics", "site_url": "https://www.xpics.com", "description": "Adult image galleries", "category": "images"},
        {"site_name": "PornPics", "site_url": "https://www.pornpics.com", "description": "Adult photo galleries", "category": "images"},
        {"site_name": "Eporner", "site_url": "https://www.eporner.com", "description": "Adult videos and images", "category": "mixed"},
        
        # Alternative/Niche Sites
        {"site_name": "Heavy-R", "site_url": "https://heavy-r.com", "description": "Extreme adult content", "category": "extreme"},
        {"site_name": "ThisVid", "site_url": "https://thisvid.com", "description": "Adult video sharing", "category": "video"},
        {"site_name": "PornHub Gay", "site_url": "https://www.pornhub.com/gay", "description": "Gay adult content", "category": "gay"},
        {"site_name": "XVideos Gay", "site_url": "https://www.xvideos.com/gay", "description": "Gay adult videos", "category": "gay"},
        {"site_name": "Xtube", "site_url": "https://www.xtube.com", "description": "Adult video platform", "category": "video"},
        
        # International Sites
        {"site_name": "Txxx", "site_url": "https://www.txxx.com", "description": "International adult videos", "category": "video"},
        {"site_name": "VPorn", "site_url": "https://www.vporn.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "Ah-Me", "site_url": "https://www.ah-me.com", "description": "Asian adult content", "category": "asian"},
        {"site_name": "JavHD", "site_url": "https://javhd.com", "description": "Japanese adult videos", "category": "asian"},
        
        # Adult Forums/Communities
        {"site_name": "AdultDVDTalk", "site_url": "https://forum.adultdvdtalk.com", "description": "Adult industry forum", "category": "forum"},
        {"site_name": "FreeOnes", "site_url": "https://www.freeones.com", "description": "Adult performer database", "category": "database"},
        
        # Tube Aggregators
        {"site_name": "Porn.com", "site_url": "https://www.porn.com", "description": "Adult video aggregator", "category": "aggregator"},
        {"site_name": "Fuq", "site_url": "https://www.fuq.com", "description": "Adult video search", "category": "aggregator"},
        {"site_name": "NudeVista", "site_url": "https://www.nudevista.com", "description": "Adult content search", "category": "aggregator"},
        {"site_name": "Pornktube", "site_url": "https://www.pornktube.com", "description": "Adult video platform", "category": "video"},
        
        # Mobile-Focused Sites
        {"site_name": "MobilePorn", "site_url": "https://www.mobileporn.com", "description": "Mobile adult videos", "category": "mobile"},
        {"site_name": "PornMD", "site_url": "https://www.pornmd.com", "description": "Adult video search", "category": "search"},
        
        # Alternative Platforms
        {"site_name": "Motherless", "site_url": "https://motherless.com", "description": "User-generated adult content", "category": "amateur"},
        {"site_name": "Empflix", "site_url": "https://www.empflix.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "DrTuber", "site_url": "https://www.drtuber.com", "description": "Adult video site", "category": "video"},
        {"site_name": "AlohaTube", "site_url": "https://www.alohatube.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "PornOne", "site_url": "https://pornone.com", "description": "Adult video aggregator", "category": "video"},
        
        # Specialized Categories
        {"site_name": "Lesbian8", "site_url": "https://lesbian8.com", "description": "Lesbian adult content", "category": "lesbian"},
        {"site_name": "MatureTube", "site_url": "https://www.maturetube.com", "description": "Mature adult content", "category": "mature"},
        {"site_name": "TeenPorn", "site_url": "https://www.teenporn.com", "description": "Teen category adult content", "category": "teen"},
        {"site_name": "AnalPorn", "site_url": "https://www.analporn.com", "description": "Anal category content", "category": "anal"},
        {"site_name": "BBWTube", "site_url": "https://www.bbwtube.com", "description": "BBW category content", "category": "bbw"}
    ]
    
    print(f"Populating database with {len(adult_sites)} adult sites...")
    
    success_count = 0
    for site in adult_sites:
        try:
            site_id = db.insert_stored_site(site)
            if site_id:
                success_count += 1
                print(f"✓ Added: {site['site_name']}")
            else:
                print(f"✗ Failed to add: {site['site_name']}")
        except Exception as e:
            print(f"✗ Error adding {site['site_name']}: {e}")
    
    print(f"\nCompleted! Successfully added {success_count}/{len(adult_sites)} sites.")
    db.disconnect()

if __name__ == "__main__":
    populate_adult_sites()
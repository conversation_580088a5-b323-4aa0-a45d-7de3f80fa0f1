#!/usr/bin/env python3
"""
Advanced Adult Site Scraper with Age Verification Bypass
Handles JavaScript rendering, cookie consent, and multi-page scraping
"""

import asyncio
import json
import sys
import os
from playwright.async_api import async_playwright
from urllib.parse import urljoin, urlparse
import time
import random
from db_models import VideoDB

class AdvancedAdultScraper:
    def __init__(self):
        self.db = VideoDB()
        self.videos_scraped = 0
        self.max_pages = 10  # Scrape up to 10 pages per site
        self.delay_range = (2, 5)  # Random delay between requests

    async def scrape_site(self, site_url, site_name):
        """Main scraping function for a single site"""
        print(f"🚀 Starting advanced scraping of {site_name} ({site_url})")
        
        async with async_playwright() as p:
            # Launch browser with stealth settings
            browser = await p.chromium.launch(
                headless=True,  # Set to False for debugging
                args=[
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ]
            )
            
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = await context.new_page()
            
            try:
                # Navigate to site
                await page.goto(site_url, wait_until='domcontentloaded', timeout=30000)
                await self.random_delay()
                
                # Handle age verification and cookies
                await self.handle_popups(page, site_name)
                
                # Wait for content to load
                await page.wait_for_timeout(3000)
                
                # Scrape multiple pages
                for page_num in range(1, self.max_pages + 1):
                    print(f"📄 Scraping page {page_num} of {site_name}")
                    
                    videos = await self.extract_videos_from_page(page, site_url, site_name)
                    
                    if not videos:
                        print(f"⚠️ No videos found on page {page_num}, stopping")
                        break
                    
                    # Save videos to database
                    for video in videos:
                        success = self.db.insert_video(video)
                        if success:
                            self.videos_scraped += 1
                    
                    print(f"💾 Saved {len(videos)} videos from page {page_num}")
                    
                    # Try to navigate to next page
                    if page_num < self.max_pages:
                        next_page_found = await self.navigate_to_next_page(page, site_name)
                        if not next_page_found:
                            print(f"🛑 No next page found, stopping at page {page_num}")
                            break
                        await self.random_delay()
                
            except Exception as e:
                print(f"❌ Error scraping {site_name}: {e}")
            finally:
                await browser.close()
        
        print(f"✅ Completed scraping {site_name}: {self.videos_scraped} total videos")

    async def handle_popups(self, page, site_name):
        """Handle age verification and cookie popups"""
        print(f"🔞 Handling popups for {site_name}")
        
        # Common age verification selectors
        age_selectors = [
            # Generic
            'button:has-text("I am 18")', 'button:has-text("18+")', 'button:has-text("Enter")',
            'a:has-text("I am 18")', 'a:has-text("18+")', 'a:has-text("Enter")',
            '.age-verification button', '.age-check button', '#age-verification button',
            
            # Site-specific
            '.warning button', '.age-warning button', '.disclaimer button',
            '[data-testid="age-verification"]', '[data-testid="enter-site"]',
            '.enter-site', '.age-gate button', '.content-warning button'
        ]
        
        # Cookie consent selectors
        cookie_selectors = [
            'button:has-text("Accept")', 'button:has-text("Allow")', 'button:has-text("OK")',
            'button:has-text("I agree")', 'button:has-text("Accept all")',
            '.cookie-consent button', '#cookie-consent button', '.gdpr-consent button',
            '[data-testid="accept-cookies"]', '.cookie-banner button'
        ]
        
        # Try age verification first
        for selector in age_selectors:
            try:
                element = page.locator(selector).first
                if await element.is_visible(timeout=2000):
                    print(f"🔞 Found age verification button: {selector}")
                    await element.click()
                    await page.wait_for_timeout(2000)
                    break
            except:
                continue
        
        # Then handle cookies
        for selector in cookie_selectors:
            try:
                element = page.locator(selector).first
                if await element.is_visible(timeout=2000):
                    print(f"🍪 Found cookie consent button: {selector}")
                    await element.click()
                    await page.wait_for_timeout(1000)
                    break
            except:
                continue

    async def extract_videos_from_page(self, page, base_url, site_name):
        """Extract video metadata from current page"""
        videos = []
        
        # Enhanced selectors for adult sites
        video_selectors = [
            # PornHub
            '.phimage', '.videoblock', '.wrap', '.thumbnail-info-wrapper',
            # XVideos  
            '.mozaique', '.thumb-block', '.thumb', '.video-block',
            # RedTube
            '.video_link', '.video-item', '.thumb', '.videoThumb',
            # Generic
            '.video-card', '.video-item', '.video-wrapper', '.thumb-container',
            '[class*="video"]', '[class*="thumb"]', '[data-video-id]',
            'a[href*="/video/"]', 'a[href*="/watch/"]', 'a[href*="/v/"]'
        ]
        
        print(f"🔍 Extracting videos from {site_name}")
        
        for selector in video_selectors:
            try:
                elements = await page.locator(selector).all()
                print(f"📹 Found {len(elements)} elements with selector: {selector}")
                
                for i, element in enumerate(elements):
                    try:
                        video_data = await self.extract_video_data(element, base_url, i)
                        if video_data and video_data['title'] and video_data['original_url']:
                            videos.append(video_data)
                            if len(videos) >= 50:  # Limit per page to avoid overwhelming
                                print(f"📈 Reached 50 videos limit for this page")
                                return videos
                    except Exception as e:
                        print(f"⚠️ Error extracting video {i}: {e}")
                        continue
                
                if videos:
                    break  # Found videos with this selector, no need to try others
                    
            except Exception as e:
                print(f"⚠️ Error with selector {selector}: {e}")
                continue
        
        return videos

    async def extract_video_data(self, element, base_url, index):
        """Extract individual video data from element"""
        try:
            # Extract title
            title = ""
            title_selectors = ['title', 'alt', 'data-title', '.title', 'h3', 'h4', '.video-title']
            for attr in title_selectors:
                if attr.startswith('.') or attr in ['h3', 'h4']:
                    # CSS selector
                    try:
                        title_elem = element.locator(attr).first
                        if await title_elem.is_visible():
                            title = await title_elem.text_content()
                            if title:
                                break
                    except:
                        continue
                else:
                    # Attribute
                    title = await element.get_attribute(attr)
                    if title:
                        break
            
            # Extract URL
            original_url = ""
            if await element.get_attribute('href'):
                original_url = await element.get_attribute('href')
            else:
                # Look for parent or child links
                try:
                    link = element.locator('a').first
                    if await link.is_visible():
                        original_url = await link.get_attribute('href')
                except:
                    pass
            
            # Make URL absolute
            if original_url and not original_url.startswith('http'):
                original_url = urljoin(base_url, original_url)
            
            # Extract thumbnail
            thumbnail_url = ""
            img_selectors = ['img', '.thumb img', '.thumbnail img']
            for selector in img_selectors:
                try:
                    img = element.locator(selector).first
                    if await img.is_visible():
                        src = await img.get_attribute('src') or await img.get_attribute('data-src')
                        if src:
                            thumbnail_url = urljoin(base_url, src) if not src.startswith('http') else src
                            break
                except:
                    continue
            
            # Generate video ID
            video_id = f"{urlparse(base_url).netloc}_{hash(original_url or title)}_{index}"
            
            # Extract description (try to get more context)
            description = ""
            try:
                desc_elem = element.locator('.description, .info, p').first
                if await desc_elem.is_visible():
                    description = await desc_elem.text_content()
            except:
                pass
            
            if not title:
                title = f"Video from {urlparse(base_url).netloc}"
            
            if not original_url:
                original_url = f"{base_url}#{video_id}"
            
            if not thumbnail_url:
                thumbnail_url = "https://via.placeholder.com/320x200?text=Adult+Video"
            
            return {
                "video_id": video_id,
                "title": title.strip()[:200],  # Limit title length
                "original_url": original_url,
                "thumbnail_url": thumbnail_url, 
                "description": description.strip()[:500] if description else "",
                "smart_search_context_keywords_explicit": self.generate_keywords(title, description),
                "smart_search_context_phrases_explicit": "",
                "raw_ocr_text": "",
                "raw_image_labels": ""
            }
            
        except Exception as e:
            print(f"⚠️ Error extracting video data: {e}")
            return None

    def generate_keywords(self, title, description):
        """Generate search keywords from title and description"""
        text = f"{title} {description}".lower()
        # Extract explicit keywords commonly found in adult content
        keywords = []
        adult_terms = ['sex', 'porn', 'xxx', 'adult', 'explicit', 'nsfw', 'milf', 'teen', 'amateur', 'anal', 'oral', 'hardcore', 'softcore']
        
        for term in adult_terms:
            if term in text:
                keywords.append(term)
        
        return ", ".join(keywords)

    async def navigate_to_next_page(self, page, site_name):
        """Navigate to next page if available"""
        next_selectors = [
            'a:has-text("Next")', 'a:has-text(">")', '.next', '.pagination-next',
            '[data-testid="next-page"]', '.page-next', 'a.next',
            '.pagination a:last-child', '.pager .next'
        ]
        
        for selector in next_selectors:
            try:
                next_button = page.locator(selector).first
                if await next_button.is_visible() and await next_button.is_enabled():
                    print(f"➡️ Found next page button: {selector}")
                    await next_button.click()
                    await page.wait_for_load_state('domcontentloaded')
                    return True
            except:
                continue
        
        return False

    async def random_delay(self):
        """Random delay to avoid detection"""
        delay = random.uniform(*self.delay_range)
        await asyncio.sleep(delay)

async def main():
    if len(sys.argv) != 3:
        print("Usage: python advanced_scraper.py <site_url> <site_name>")
        sys.exit(1)
    
    site_url = sys.argv[1]
    site_name = sys.argv[2]
    
    scraper = AdvancedAdultScraper()
    await scraper.scrape_site(site_url, site_name)
    
    print(f"🎉 Total videos scraped: {scraper.videos_scraped}")

if __name__ == "__main__":
    asyncio.run(main())
version: '3.8'

services:
  dirty-search-db:
    image: postgres:16-alpine
    restart: always
    environment:
      POSTGRES_DB: ${DB_NAME:-dirty_search_db}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./db_schema.sql:/docker-entrypoint-initdb.d/init.sql # Initialize schema on first run
    networks:
      - dirty-search-network

  dirty-search-elasticsearch:
    image: elastic/elasticsearch:8.14.0 # Use a specific version
    restart: always
    environment:
      discovery.type: single-node
      ES_JAVA_OPTS: -Xms512m -Xmx512m # Adjust memory as needed
      # For Elastic 8.x, security is enabled by default.
      # You might need to disable it for local dev or configure users.
      # E.g., setting xpack.security.enabled=false might simplify local setup:
      xpack.security.enabled: false 
      xpack.security.http.ssl.enabled: false
      xpack.security.transport.ssl.enabled: false
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data
    networks:
      - dirty-search-network

  dirty-search-backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    restart: always
    environment:
      DB_HOST: dirty-search-db
      DB_NAME: ${DB_NAME:-dirty_search_db}
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      DB_PORT: ${DB_PORT:-5432}
      ELASTICSEARCH_HOST: http://dirty-search-elasticsearch:9200 # Internal Docker network hostname
      # You might add ELASTICSEARCH_USER and ELASTICSEARCH_PASSWORD here if security is enabled
    ports:
      - "8000:8000"
    depends_on:
      - dirty-search-db
      - dirty-search-elasticsearch
    networks:
      - dirty-search-network

  dirty-search-frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.frontend
    restart: always
    ports:
      - "80:80" # Serve frontend on default HTTP port
    depends_on:
      - dirty-search-backend # Depends on backend to ensure it's up, though not strictly required for static files
    networks:
      - dirty-search-network


volumes:
  db_data:
  es_data:


networks:
  dirty-search-network:
    driver: bridge
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Player - Dirty Search</title>
    <link rel="stylesheet" href="style.css">
    <style>
        .video-player-container {
            max-width: 1200px;
            margin: 20px auto;
            background: #000;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .video-player {
            width: 100%;
            height: 600px;
            background: #000;
        }
        
        .player-controls {
            background: #1a1a1a;
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .quality-selector select {
            background: #333;
            color: white;
            border: 1px solid #555;
            padding: 5px 10px;
            border-radius: 4px;
        }
        
        .video-info {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
        }
        
        .video-title {
            font-size: 24px;
            margin-bottom: 10px;
            color: #ff6b6b;
        }
        
        .video-description {
            color: #ccc;
            line-height: 1.6;
        }
        
        .back-button {
            background: #ff6b6b;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px;
            text-decoration: none;
            display: inline-block;
        }
        
        .back-button:hover {
            background: #ff5252;
        }
        
        .error-message {
            background: #ff4444;
            color: white;
            padding: 15px;
            margin: 20px;
            border-radius: 4px;
            text-align: center;
        }
        
        .loading {
            text-align: center;
            padding: 50px;
            color: #ccc;
        }
    </style>
</head>
<body>
    <header>
        <h1>Dirty Search Player</h1>
        <a href="index.html" class="back-button">← Back to Search</a>
    </header>

    <main>
        <div id="loading" class="loading">
            <h3>Loading video...</h3>
        </div>
        
        <div id="error" class="error-message" style="display: none;">
            <h3>Video not available</h3>
            <p>This video cannot be played directly. Click the link below to view on the original site.</p>
        </div>
        
        <div id="player-container" class="video-player-container" style="display: none;">
            <video id="video-player" class="video-player" controls preload="metadata">
                <p>Your browser doesn't support HTML5 video.</p>
            </video>
            
            <div class="player-controls">
                <div class="quality-selector">
                    <label for="quality">Quality:</label>
                    <select id="quality">
                        <option value="1080">1080p</option>
                        <option value="720" selected>720p</option>
                        <option value="480">480p</option>
                        <option value="360">360p</option>
                    </select>
                </div>
                
                <button id="fullscreen-btn">Fullscreen</button>
                <button id="download-btn">Download</button>
                
                <div style="margin-left: auto;">
                    <span id="video-stats"></span>
                </div>
            </div>
        </div>
        
        <div id="video-info" class="video-info" style="display: none;">
            <div id="video-title" class="video-title"></div>
            <div id="video-description" class="video-description"></div>
            <div id="original-link" style="margin-top: 15px;"></div>
        </div>
    </main>

    <script>
        const API_BASE_URL = 'http://dirty-search-backend:8000';
        
        class VideoPlayer {
            constructor() {
                this.videoElement = document.getElementById('video-player');
                this.qualitySelect = document.getElementById('quality');
                this.fullscreenBtn = document.getElementById('fullscreen-btn');
                this.downloadBtn = document.getElementById('download-btn');
                this.videoStats = document.getElementById('video-stats');
                
                this.currentVideoData = null;
                
                this.initializePlayer();
                this.loadVideoFromURL();
            }
            
            initializePlayer() {
                // Quality change handler
                this.qualitySelect.addEventListener('change', () => {
                    this.changeQuality(this.qualitySelect.value);
                });
                
                // Fullscreen handler
                this.fullscreenBtn.addEventListener('click', () => {
                    this.toggleFullscreen();
                });
                
                // Download handler
                this.downloadBtn.addEventListener('click', () => {
                    this.downloadVideo();
                });
                
                // Video stats updater
                this.videoElement.addEventListener('loadedmetadata', () => {
                    this.updateVideoStats();
                });
                
                this.videoElement.addEventListener('timeupdate', () => {
                    this.updateProgress();
                });
            }
            
            async loadVideoFromURL() {
                const urlParams = new URLSearchParams(window.location.search);
                const videoId = urlParams.get('id');
                
                if (!videoId) {
                    this.showError('No video ID provided');
                    return;
                }
                
                try {
                    const response = await fetch(`${API_BASE_URL}/api/videos`);
                    const data = await response.json();
                    
                    const video = data.videos.find(v => v.video_id === videoId);
                    if (!video) {
                        this.showError('Video not found');
                        return;
                    }
                    
                    this.currentVideoData = video;
                    this.displayVideoInfo(video);
                    
                    // Try to load video - for now, show original link since direct streaming needs more setup
                    this.showOriginalLink(video);
                    
                } catch (error) {
                    console.error('Error loading video:', error);
                    this.showError('Failed to load video');
                }
            }
            
            displayVideoInfo(video) {
                document.getElementById('video-title').textContent = video.title;
                document.getElementById('video-description').textContent = video.description || 'No description available';
                document.getElementById('video-info').style.display = 'block';
            }
            
            showOriginalLink(video) {
                document.getElementById('loading').style.display = 'none';
                
                const linkContainer = document.getElementById('original-link');
                linkContainer.innerHTML = `
                    <p style="color: #ccc; margin-bottom: 10px;">Direct streaming not available for this video.</p>
                    <a href="${video.original_url}" target="_blank" class="back-button" style="margin: 0;">
                        Watch on ${new URL(video.original_url).hostname}
                    </a>
                `;
            }
            
            showError(message) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                document.getElementById('error').querySelector('p').textContent = message;
            }
            
            changeQuality(quality) {
                if (this.videoElement.src) {
                    const currentTime = this.videoElement.currentTime;
                    // In a real implementation, you'd switch to a different quality URL
                    // For now, just store the preference
                    localStorage.setItem('preferredQuality', quality);
                }
            }
            
            toggleFullscreen() {
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    this.videoElement.requestFullscreen();
                }
            }
            
            downloadVideo() {
                if (this.currentVideoData) {
                    window.open(this.currentVideoData.original_url, '_blank');
                }
            }
            
            updateVideoStats() {
                if (this.videoElement.videoWidth) {
                    const resolution = `${this.videoElement.videoWidth}×${this.videoElement.videoHeight}`;
                    const duration = this.formatTime(this.videoElement.duration);
                    this.videoStats.textContent = `${resolution} • ${duration}`;
                }
            }
            
            updateProgress() {
                // Add any progress tracking here
            }
            
            formatTime(seconds) {
                const minutes = Math.floor(seconds / 60);
                const secs = Math.floor(seconds % 60);
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }
        
        // Initialize player when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new VideoPlayer();
        });
    </script>
</body>
</html>
// Use environment-aware URLs for <PERSON><PERSON> vs local development
const API_BASE_URL = 'http://localhost:8000';

const SEARCH_API_URL = `${API_BASE_URL}/api/search`; // FastAPI backend search endpoint
const VIDEOS_API_URL = `${API_BASE_URL}/api/videos`; // FastAPI backend list endpoint
const SCRAPE_API_URL = `${API_BASE_URL}/api/scrape-url`; // FastAPI backend scrape endpoint
const STORED_SITES_API_URL = `${API_BASE_URL}/api/stored-sites`; // FastAPI stored sites endpoint
const SCRAPE_STORED_SITE_API_URL = `${API_BASE_URL}/api/scrape-stored-site`; // FastAPI scrape stored site endpoint

const searchInput = document.getElementById('search-input');
const searchButton = document.getElementById('search-button');
const videoResultsDiv = document.getElementById('video-results');
const prevPageButton = document.getElementById('prev-page');
const nextPageButton = document.getElementById('next-page');
const currentPageSpan = document.getElementById('current-page');
const videoCountElement = document.getElementById('video-count');

// URL scraping elements
const urlInput = document.getElementById('url-input');
const scrapeButton = document.getElementById('scrape-button');
const scrapingStatus = document.getElementById('scraping-status');

// Stored sites elements
const sitesSelect = document.getElementById('sites-select');
const scrapeSelectedSiteButton = document.getElementById('scrape-selected-site');
const deleteSelectedSiteButton = document.getElementById('delete-selected-site');

let currentPage = 1;
const videosPerPage = 1000; // Show many more videos without pagination limits

async function fetchVideos(query = '', page = 1) {
    let url = '';
    const offset = (page - 1) * videosPerPage;

    if (query) {
        url = `${SEARCH_API_URL}?q=${encodeURIComponent(query)}&limit=${videosPerPage}&offset=${offset}`;
    } else {
        url = `${VIDEOS_API_URL}?limit=${videosPerPage}&offset=${offset}`;
    }

    try {
        const response = await fetch(url);
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP error! status: ${response.status} - ${errorData.detail || response.statusText}`);
        }
        const data = await response.json();
        console.log('API Response:', data); // Log the full API response for debugging
        return data;
    } catch (error) {
        console.error('Error fetching videos:', error);
        videoResultsDiv.innerHTML = `<p style="color: red;">Error loading videos: ${error.message}. Please ensure the backend API is running at ${url}</p>`;
        return { videos: [], total: 0 };
    }
}

function displayVideos(videos) {
    videoResultsDiv.innerHTML = ''; // Clear previous results
    if (videos.length === 0) {
        videoResultsDiv.innerHTML = '<p>No videos found. Try a different search or check backend.</p>';
        return;
    }

    videos.forEach(video => {
        const videoCard = document.createElement('div');
        videoCard.className = 'video-card';

        const thumbnailUrl = video.thumbnail_url || 'https://via.placeholder.com/280x180?text=No+Thumbnail';
        const title = video.title || 'Untitled Video';
        const originalUrl = video.original_url || '#';
        const description = video.description || 'No description available.';
        const scrapedDate = video.scraped_date ? new Date(video.scraped_date).toLocaleDateString() : 'N/A';
        const rawOcrText = video.raw_ocr_text ? `OCR: ${video.raw_ocr_text.substring(0, 100)}${video.raw_ocr_text.length > 100 ? '...' : ''}` : '';
        const rawImageLabels = video.raw_image_labels ? `Labels: ${video.raw_image_labels.substring(0, 100)}${video.raw_image_labels.length > 100 ? '...' : ''}` : '';

        videoCard.style.cursor = 'pointer';
        
        videoCard.innerHTML = `
            <img src="${thumbnailUrl}" alt="${title}" class="video-thumbnail" onerror="handleThumbnailError(this)" loading="lazy">
            <div class="video-info">
                <h3 class="video-title">${title}</h3>
                <p class="description">${description}</p>
                <div class="video-actions">
                    <button class="play-button" onclick="event.stopPropagation(); openVideoPlayer('${video.video_id}')">▶ Play</button>
                    <a href="${originalUrl}" target="_blank" class="external-link" onclick="event.stopPropagation()">External Link</a>
                </div>
                <div class="video-meta">
                    <p>Scraped: ${scrapedDate}</p>
                    ${rawOcrText ? `<p>${rawOcrText}</p>` : ''}
                    ${rawImageLabels ? `<p>${rawImageLabels}</p>` : ''}
                </div>
            </div>
        `;
        
        // Make entire card clickable
        videoCard.addEventListener('click', () => {
            openVideoPlayer(video.video_id);
        });
        
        videoResultsDiv.appendChild(videoCard);
    });
}

// Video loading workarounds
function handleThumbnailError(img) {
    console.log('Thumbnail failed to load:', img.src);
    
    // Try alternative thumbnail sources with retry mechanism
    const fallbacks = [
        'https://via.placeholder.com/280x180?text=Loading+Error',
        'https://placehold.co/280x180/333/white?text=No+Thumbnail',
        'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDI4MCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjI4MCIgaGVpZ2h0PSIxODAiIGZpbGw9IiMzMzMiLz48dGV4dCB4PSIxNDAiIHk9IjkwIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg=='
    ];
    
    const currentIndex = img.dataset.fallbackIndex ? parseInt(img.dataset.fallbackIndex) : 0;
    
    if (currentIndex < fallbacks.length) {
        img.dataset.fallbackIndex = currentIndex + 1;
        img.src = fallbacks[currentIndex];
    } else {
        // Final fallback - hide image and show text placeholder
        img.style.display = 'none';
        const placeholder = document.createElement('div');
        placeholder.className = 'thumbnail-placeholder';
        placeholder.textContent = 'No Thumbnail Available';
        placeholder.style.cssText = `
            width: 280px;
            height: 180px;
            background: #333;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        `;
        img.parentNode.insertBefore(placeholder, img);
    }
}

// Enhanced video loading with CORS proxy fallbacks
function createProxyUrl(originalUrl) {
    // List of CORS proxy services for video loading
    const proxyServices = [
        originalUrl, // Try original first
        `https://cors-anywhere.herokuapp.com/${originalUrl}`,
        `https://api.allorigins.win/raw?url=${encodeURIComponent(originalUrl)}`,
        `https://thingproxy.freeboard.io/fetch/${originalUrl}`
    ];
    
    return proxyServices;
}

// Video loading retry mechanism
async function loadVideoWithRetry(videoUrl, maxRetries = 3) {
    const proxyUrls = createProxyUrl(videoUrl);
    
    for (let i = 0; i < proxyUrls.length && i < maxRetries; i++) {
        try {
            const response = await fetch(proxyUrls[i], {
                method: 'HEAD',
                mode: 'cors'
            });
            
            if (response.ok) {
                return proxyUrls[i];
            }
        } catch (error) {
            console.log(`Video load attempt ${i + 1} failed:`, error);
            if (i === proxyUrls.length - 1) {
                console.warn('All video loading attempts failed for:', videoUrl);
                return videoUrl; // Return original as last resort
            }
        }
    }
    
    return videoUrl;
}

async function updateVideoList() {
    const query = searchInput.value.trim();
    const data = await fetchVideos(query, currentPage);
    
    // Update video count display
    updateVideoCount(data.total, query);
    
    displayVideos(data.videos);
    updatePaginationControls(data.total);
}

function updateVideoCount(total, query) {
    if (query) {
        videoCountElement.textContent = `Found ${total} videos matching "${query}"`;
    } else {
        videoCountElement.textContent = `Total Videos: ${total}`;
    }
}

function updatePaginationControls(totalVideos) {
    const totalPages = Math.ceil(totalVideos / videosPerPage);
    currentPageSpan.textContent = `Page ${currentPage} of ${totalPages || 1}`;
    
    prevPageButton.disabled = currentPage === 1;
    nextPageButton.disabled = currentPage === totalPages || totalVideos === 0;
}

// Event Listeners
searchButton.addEventListener('click', () => {
    currentPage = 1; // Reset to first page on new search
    updateVideoList();
});

searchInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        currentPage = 1; // Reset to first page on new search
        updateVideoList();
    }
});

prevPageButton.addEventListener('click', () => {
    if (currentPage > 1) {
        currentPage--;
        updateVideoList();
    }
});

nextPageButton.addEventListener('click', () => {
    currentPage++;
    updateVideoList();
});

// URL Scraping Functions
async function scrapeUrl(url) {
    try {
        scrapingStatus.className = 'loading';
        scrapingStatus.textContent = 'Scraping URL for videos...';
        scrapeButton.disabled = true;

        const response = await fetch(SCRAPE_API_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url })
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP error! status: ${response.status} - ${errorData.detail || response.statusText}`);
        }

        const data = await response.json();
        scrapingStatus.className = 'success';
        scrapingStatus.textContent = `Successfully initiated scraping for ${url}. Found ${data.videos_found || 0} videos to process.`;
        
        // Clear the input and refresh the video list after a short delay
        urlInput.value = '';
        setTimeout(() => {
            updateVideoList();
            scrapingStatus.textContent = '';
            scrapingStatus.className = '';
        }, 3000);

    } catch (error) {
        console.error('Error scraping URL:', error);
        scrapingStatus.className = 'error';
        scrapingStatus.textContent = `Error scraping URL: ${error.message}`;
    } finally {
        scrapeButton.disabled = false;
    }
}

// URL Scraping Event Listeners
scrapeButton.addEventListener('click', () => {
    const url = urlInput.value.trim();
    if (!url) {
        scrapingStatus.className = 'error';
        scrapingStatus.textContent = 'Please enter a valid URL';
        return;
    }
    scrapeUrl(url);
});

urlInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
        const url = urlInput.value.trim();
        if (!url) {
            scrapingStatus.className = 'error';
            scrapingStatus.textContent = 'Please enter a valid URL';
            return;
        }
        scrapeUrl(url);
    }
});

// Stored Sites Functions
async function loadStoredSites() {
    try {
        const response = await fetch(STORED_SITES_API_URL);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        populateStoredSitesDropdown(data.sites);
    } catch (error) {
        console.error('Error loading stored sites:', error);
    }
}

function populateStoredSitesDropdown(sites) {
    // Clear existing options except the first placeholder
    sitesSelect.innerHTML = '<option value="">Select a stored site...</option>';
    
    sites.forEach(site => {
        const option = document.createElement('option');
        option.value = site.site_id;
        option.textContent = `${site.site_name} (${site.category})`;
        sitesSelect.appendChild(option);
    });
}


async function scrapeStoredSiteById(siteId) {
    try {
        scrapingStatus.className = 'loading';
        scrapingStatus.textContent = 'Scraping stored site...';
        
        const response = await fetch(`${SCRAPE_STORED_SITE_API_URL}/${siteId}`, {
            method: 'POST'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP error! status: ${response.status} - ${errorData.detail || response.statusText}`);
        }
        
        const data = await response.json();
        scrapingStatus.className = 'success';
        scrapingStatus.textContent = `Successfully initiated scraping for ${data.url}`;
        
        setTimeout(() => {
            updateVideoList();
            scrapingStatus.textContent = '';
            scrapingStatus.className = '';
        }, 3000);
        
    } catch (error) {
        console.error('Error scraping stored site:', error);
        scrapingStatus.className = 'error';
        scrapingStatus.textContent = `Error scraping site: ${error.message}`;
    }
}

async function deleteStoredSite(siteId) {
    if (!confirm('Are you sure you want to delete this stored site?')) {
        return;
    }
    
    try {
        const response = await fetch(`${STORED_SITES_API_URL}/${siteId}`, {
            method: 'DELETE'
        });
        
        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`HTTP error! status: ${response.status} - ${errorData.detail || response.statusText}`);
        }
        
        // Reload the stored sites list
        loadStoredSites();
        
    } catch (error) {
        console.error('Error deleting stored site:', error);
        alert(`Error deleting site: ${error.message}`);
    }
}

// Stored Sites Event Listeners
sitesSelect.addEventListener('change', () => {
    const hasSelection = sitesSelect.value !== '';
    scrapeSelectedSiteButton.disabled = !hasSelection;
    deleteSelectedSiteButton.disabled = !hasSelection;
});

scrapeSelectedSiteButton.addEventListener('click', () => {
    const selectedSiteId = sitesSelect.value;
    if (selectedSiteId) {
        scrapeStoredSiteById(parseInt(selectedSiteId));
        sitesSelect.value = ''; // Reset selection
        scrapeSelectedSiteButton.disabled = true;
        deleteSelectedSiteButton.disabled = true;
    }
});

deleteSelectedSiteButton.addEventListener('click', () => {
    const selectedSiteId = sitesSelect.value;
    if (selectedSiteId) {
        deleteStoredSite(parseInt(selectedSiteId));
        sitesSelect.value = ''; // Reset selection
        scrapeSelectedSiteButton.disabled = true;
        deleteSelectedSiteButton.disabled = true;
    }
});

// Video Player Function
function openVideoPlayer(videoId) {
    // Open video player in new tab
    window.open(`player.html?id=${videoId}`, '_blank');
}

// Initial load
updateVideoList();
loadStoredSites();
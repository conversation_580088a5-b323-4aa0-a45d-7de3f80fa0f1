body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #1a1a1a;
    color: #f0f0f0;
    line-height: 1.6;
}

header {
    background-color: #333;
    color: #fff;
    padding: 1rem 0;
    text-align: center;
    border-bottom: 2px solid #555;
}

h1, h2 {
    color: #e0e0e0;
    margin-top: 0;
}

main {
    padding: 20px;
    max-width: 1200px;
    margin: 20px auto;
    background-color: #2a2a2a;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.5);
}

#url-scraping-section {
    background-color: #333;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    border: 2px solid #555;
}

#url-scraping-section h2 {
    color: #ff6666;
    margin-bottom: 15px;
    text-align: center;
}

#scraping-form {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

#url-input {
    width: 60%;
    padding: 12px;
    border: 1px solid #555;
    border-radius: 5px;
    background-color: #3a3a3a;
    color: #f0f0f0;
    font-size: 1rem;
}

#url-input::placeholder {
    color: #bbb;
}

#scrape-button {
    padding: 12px 25px;
    background-color: #006600;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

#scrape-button:hover {
    background-color: #008800;
}

#scrape-button:disabled {
    background-color: #444;
    color: #777;
    cursor: not-allowed;
}

#scraping-status {
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

#scraping-status.success {
    background-color: #2d5a3d;
    color: #88ff88;
    border: 1px solid #66cc66;
}

#scraping-status.error {
    background-color: #5a2d2d;
    color: #ff8888;
    border: 1px solid #cc6666;
}

#scraping-status.loading {
    background-color: #3d3d5a;
    color: #8888ff;
    border: 1px solid #6666cc;
}

/* Stored Sites Section */
#stored-sites-section {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #555;
}

#stored-sites-section h3 {
    color: #ff9999;
    margin-bottom: 15px;
    text-align: center;
}

#stored-sites-dropdown {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

#sites-select {
    width: 60%;
    padding: 12px;
    border: 1px solid #555;
    border-radius: 5px;
    background-color: #3a3a3a;
    color: #f0f0f0;
    font-size: 1rem;
}

#scrape-selected-site {
    padding: 12px 25px;
    background-color: #cc6600;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

#scrape-selected-site:hover:not(:disabled) {
    background-color: #ee8800;
}

#scrape-selected-site:disabled {
    background-color: #444;
    color: #777;
    cursor: not-allowed;
}

#stored-sites-list {
    max-height: 300px;
    overflow-y: auto;
    background-color: #2a2a2a;
    border-radius: 5px;
    border: 1px solid #555;
}

.site-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #444;
    transition: background-color 0.2s ease;
}

.site-item:last-child {
    border-bottom: none;
}

.site-item:hover {
    background-color: #3a3a3a;
}

.site-info {
    flex-grow: 1;
    min-width: 0;
}

.site-info .site-name {
    font-weight: bold;
    color: #e0e0e0;
    margin-bottom: 3px;
}

.site-info .site-url {
    font-size: 0.85em;
    color: #bbb;
    word-break: break-all;
}

.site-actions {
    display: flex;
    gap: 8px;
    flex-shrink: 0;
}

.delete-site-btn, .scrape-site-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.2s ease;
}

.delete-site-btn {
    background-color: #aa0000;
    color: #fff;
}

.delete-site-btn:hover {
    background-color: #cc0000;
}

.scrape-site-btn {
    background-color: #006600;
    color: #fff;
}

.scrape-site-btn:hover {
    background-color: #008800;
}

#search-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
}

#search-section h2 {
    margin-bottom: 15px;
}

#search-section > div {
    display: flex;
    justify-content: center;
    gap: 10px;
    width: 100%;
}

#search-input {
    width: 60%;
    padding: 12px;
    border: 1px solid #555;
    border-radius: 5px;
    background-color: #3a3a3a;
    color: #f0f0f0;
    font-size: 1rem;
}

#search-input::placeholder {
    color: #bbb;
}

#search-button {
    padding: 12px 25px;
    background-color: #880000;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

#search-button:hover {
    background-color: #aa0000;
}

#video-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.video-card {
    background-color: #333;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    flex-direction: column;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4);
    cursor: pointer;
}

.video-card.clickable {
    cursor: pointer;
}

.video-card img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    display: block;
    border-bottom: 1px solid #444;
}

.video-info {
    padding: 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.video-info a {
    color: #e0e0e0;
    text-decoration: none;
    font-size: 1.1em;
    font-weight: bold;
    margin-bottom: 10px;
    display: block;
}

.video-info a:hover {
    color: #ff4444;
}

.video-info p {
    font-size: 0.9em;
    color: #ccc;
    margin-bottom: 5px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit description to 3 lines */
    line-clamp: 3; /* Standard property for compatibility */
    -webkit-box-orient: vertical;
}

.video-actions {
    display: flex;
    gap: 10px;
    margin: 10px 0;
}

.play-button {
    background-color: #ff6b6b;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.play-button:hover {
    background-color: #ff5252;
}

.external-link {
    background-color: #555;
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.external-link:hover {
    background-color: #777;
}

.video-title {
    color: #e0e0e0;
    font-size: 1.1em;
    font-weight: bold;
    margin: 0 0 8px 0;
}

.video-meta {
    font-size: 0.8em;
    color: #aaa;
    margin-top: auto; /* Pushes meta info to the bottom */
    padding-top: 10px;
    border-top: 1px solid #444;
}

#pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
    gap: 15px;
    font-size: 1.1em;
}

#pagination-controls button {
    padding: 10px 20px;
    background-color: #555;
    color: #fff;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

#pagination-controls button:hover:not(:disabled) {
    background-color: #777;
}

#pagination-controls button:disabled {
    background-color: #3a3a3a;
    color: #777;
    cursor: not-allowed;
}

/* Video Count Display */
#video-count-container {
    margin-bottom: 1rem;
    padding: 1rem;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 8px;
    border-left: 4px solid #e74c3c;
}

#video-count {
    color: #e74c3c;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Thumbnail Loading Workarounds */
.thumbnail-placeholder {
    background: #333 !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 14px !important;
    border-radius: 8px;
    width: 280px;
    height: 180px;
}

.video-thumbnail {
    transition: opacity 0.3s ease;
}

.video-thumbnail[src*="placeholder"] {
    opacity: 0.7;
}

footer {
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    background-color: #333;
    color: #ccc;
    border-top: 2px solid #555;
    font-size: 0.9em;
}
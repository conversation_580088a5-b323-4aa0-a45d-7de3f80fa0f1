<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dirty Search - Uncensored Video Lister & Search</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>Dirty Search</h1>
        <p>Your uncensored video hub.</p>
    </header>

    <main>
        <section id="url-scraping-section">
            <h2>Add New Site to Scrape</h2>
            <div id="scraping-form">
                <input type="url" id="url-input" placeholder="Enter website URL to scrape for videos..." required>
                <button id="scrape-button">Scrape Site</button>
            </div>
            <div id="scraping-status"></div>
            
            <div id="stored-sites-section">
                <h3>Stored Sites</h3>
                <div id="stored-sites-dropdown">
                    <select id="sites-select">
                        <option value="">Select a stored site...</option>
                    </select>
                    <button id="scrape-selected-site" disabled>Scrape Selected</button>
                    <button id="delete-selected-site" disabled>Delete Selected</button>
                </div>
            </div>
        </section>

        <section id="search-section">
            <h2>Search Videos</h2>
            <div>
                <input type="text" id="search-input" placeholder="Search for uncensored content..." autofocus>
                <button id="search-button">Search</button>
            </div>
        </section>

        <section id="video-results-section">
            <div id="video-count-container">
                <h3 id="video-count">Loading videos...</h3>
            </div>
            <h2>Videos</h2>
            <div id="video-results">
                <!-- Video cards will be dynamically loaded here -->
            </div>
            <div id="pagination-controls">
                <button id="prev-page" disabled>Previous</button>
                <span id="current-page">Page 1</span>
                <button id="next-page">Next</button>
            </div>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 Dirty Search. All content displayed as-is, no censorship.</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>
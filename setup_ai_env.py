import subprocess
import sys
import os

def setup_environment():
    """
    Sets up a Python virtual environment and installs necessary dependencies.
    """
    venv_dir = "venv"
    requirements_file = "requirements_ai.txt"

    print(f"Creating virtual environment in {venv_dir}...")
    try:
        subprocess.run([sys.executable, "-m", "venv", venv_dir], check=True)
        print("Virtual environment created successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error creating virtual environment: {e}")
        sys.exit(1)

    # Ensure pip is up-to-date in the virtual environment
    pip_executable = os.path.join(venv_dir, "bin", "pip") if os.name != 'nt' else os.path.join(venv_dir, "Scripts", "pip.exe")
    try:
        subprocess.run([pip_executable, "install", "--upgrade", "pip"], check=True)
        print("Upgraded pip in virtual environment.")
    except subprocess.CalledProcessError as e:
        print(f"Error upgrading pip: {e}")
        sys.exit(1)

    print(f"Creating {requirements_file}...")
    requirements_content = """
Pillow
pytesseract
transformers
torch
uvicorn
fastapi
python-multipart
"""
    with open(requirements_file, "w") as f:
        f.write(requirements_content.strip())
    print(f"{requirements_file} created successfully.")

    print(f"Installing dependencies from {requirements_file}...")
    try:
        subprocess.run([pip_executable, "install", "-r", requirements_file], check=True)
        print("Dependencies installed successfully.")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        print("Please ensure you have an internet connection and the listed packages are available.")
        sys.exit(1)

    print("\nEnvironment setup complete!")
    print(f"To activate the virtual environment, run: source {venv_dir}/bin/activate (Linux/macOS) or .\\{venv_dir}\\Scripts\\activate (Windows)")
    print("Then you can run the AI scripts.")

if __name__ == "__main__":
    setup_environment()
CREATE TABLE videos (
    video_id VARCHAR(255) PRIMARY KEY,
    title TEXT NOT NULL,
    original_url TEXT NOT NULL,
    thumbnail_url TEXT,
    description TEXT,
    scraped_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    smart_search_context_keywords_explicit TEXT,
    smart_search_context_phrases_explicit TEXT,
    raw_ocr_text TEXT,
    raw_image_labels TEXT
);

-- Add indexes for search performance
CREATE INDEX idx_videos_title ON videos USING GIN (to_tsvector('english', title));
CREATE INDEX idx_videos_keywords ON videos USING GIN (to_tsvector('english', smart_search_context_keywords_explicit));
CREATE INDEX idx_videos_phrases ON videos USING GIN (to_tsvector('english', smart_search_context_phrases_explicit));

-- Table for storing favorite/bookmarked sites for scraping
CREATE TABLE stored_sites (
    site_id SERIAL PRIMARY KEY,
    site_name VARCHAR(255) NOT NULL,
    site_url TEXT NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(100),
    added_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_scraped TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE
);

-- Index for faster lookups
CREATE INDEX idx_stored_sites_active ON stored_sites (is_active);
CREATE INDEX idx_stored_sites_category ON stored_sites (category);
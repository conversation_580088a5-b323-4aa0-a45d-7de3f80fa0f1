import os
import sys
import subprocess
import json
import httpx # For making HTTP requests to LM Studio and downloading thumbnails
from typing import Optional
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException, Query, BackgroundTasks, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl
from bs4 import BeautifulSoup
import re

from db_models import VideoDB
from elasticsearch import Elasticsearch
import asyncio # For running async subprocesses
import psycopg2

app = FastAPI()

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust this to specific origins in a production environment
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Database connection
db = VideoDB()

# Elasticsearch connection
es_client = None
try:
    es_host = os.getenv("ELASTICSEARCH_HOST", "http://localhost:9200")
    es_client = Elasticsearch(
        es_host,
        basic_auth=(os.getenv("ELASTICSEARCH_USER", ""), os.getenv("ELASTICSEARCH_PASSWORD", ""))
    )
    if not es_client.ping():
        print(f"Warning: Could not connect to Elasticsearch at {es_host}")
        es_client = None # Set to None if connection fails
    else:
        print(f"Connected to Elasticsearch at {es_host}")
except Exception as e:
    print(f"Error connecting to Elasticsearch: {e}")
    es_client = None

@app.on_event("startup")
async def startup_event():
    db.connect()

@app.on_event("shutdown")
async def shutdown_event():
    db.disconnect()
    if es_client:
        es_client.close()

# Pydantic models for request and response validation
class VideoMetadata(BaseModel):
    video_id: str
    title: str
    original_url: HttpUrl
    thumbnail_url: HttpUrl
    description: Optional[str] = None
    smart_search_context_keywords_explicit: Optional[str] = None
    smart_search_context_phrases_explicit: Optional[str] = None
    raw_ocr_text: Optional[str] = None
    raw_image_labels: Optional[str] = None

class ProcessVideoRequest(BaseModel):
    video_id: str
    title: str
    original_url: HttpUrl
    thumbnail_url: HttpUrl
    description: Optional[str] = None

class ScrapeUrlRequest(BaseModel):
    url: HttpUrl

class StoredSiteRequest(BaseModel):
    site_name: str
    site_url: HttpUrl
    description: Optional[str] = None
    category: Optional[str] = "adult"
    
# Constants
LOCAL_AI_SCRIPTS_PATH = "./ai_scripts" # Updated path for local AI scripts
LM_STUDIO_URL = os.getenv("LM_STUDIO_URL", "http://host.docker.internal:1234") # Assuming LM Studio runs on host

@app.get("/api/videos")
async def get_videos(limit: int = 1000, offset: int = 0):
    """
    Fetches a paginated list of videos from the database.
    """
    videos_data = db.get_all_videos(limit=limit, offset=offset)
    return videos_data

@app.get("/api/search")
async def search_videos(q: str = Query(..., min_length=1), limit: int = 1000, offset: int = 0):
    """
    Searches for videos using simple LIKE queries across multiple fields,
    ensuring no internal filtering on content.
    """
    try:
        search_results = db.search_videos(q, limit=limit, offset=offset)
        return search_results
    except Exception as e:
        print(f"Search error: {e}", file=sys.stderr)
        raise HTTPException(status_code=500, detail=f"Search error: {e}")


@app.post("/api/process-video", status_code=status.HTTP_202_ACCEPTED)
async def process_video_endpoint(
    video_data: ProcessVideoRequest,
    background_tasks: BackgroundTasks
):
    """
    Receives video metadata, downloads thumbnail, performs OCR and image description,
    calls LM Studio for smart search context, and stores everything in the database.
    Runs AI processing in a background task to avoid blocking the API response.
    """
    print(f"Received video for processing: {video_data.video_id}")
    background_tasks.add_task(_process_video_data, video_data)
    return {"message": "Video processing initiated in background."}

async def _process_video_data(video_data: ProcessVideoRequest):
    """
    Internal function to handle the Heavy lifting of video processing, including AI calls.
    """
    try:
        # 1. Download Thumbnail (non-blocking - continue processing even if this fails)
        thumbnail_local_path_str = ""
        thumbnail_downloaded = False
        try:
            temp_dir = Path("/tmp/thumbnails") # Use /tmp for temporary files in Docker
            temp_dir.mkdir(parents=True, exist_ok=True)
            
            async with httpx.AsyncClient() as client:
                response = await client.get(str(video_data.thumbnail_url), timeout=30.0)
                response.raise_for_status()
                
                # Extract file extension or default to .jpg
                suffix = Path(video_data.thumbnail_url.path).suffix
                if not suffix:
                    suffix = ".jpg" # Default if no extension
                
                thumbnail_filename = f"{video_data.video_id}{suffix}"
                thumbnail_local_path = temp_dir / thumbnail_filename
                
                with open(thumbnail_local_path, "wb") as f:
                    f.write(response.content)
                thumbnail_local_path_str = str(thumbnail_local_path)
                thumbnail_downloaded = True
                print(f"Thumbnail downloaded to: {thumbnail_local_path_str}")
        except httpx.HTTPStatusError as e:
            print(f"HTTP error downloading thumbnail: {e.response.status_code} - {e.response.text} (continuing without thumbnail)", file=sys.stderr)
        except Exception as e:
            print(f"Error downloading thumbnail: {e} (continuing without thumbnail)", file=sys.stderr)

        # 2. Run OCR Script (only if thumbnail was downloaded)
        raw_ocr_text = ""
        if thumbnail_downloaded:
            ocr_result = await _run_ai_script(
                f"{LOCAL_AI_SCRIPTS_PATH}/run_ocr.py",
                thumbnail_local_path_str
            )
            raw_ocr_text = ocr_result.get("ocr_text", "")
            if ocr_result.get("error"):
                print(f"OCR Script Error: {ocr_result['error']}", file=sys.stderr)
        else:
            print("Skipping OCR processing due to thumbnail download failure", file=sys.stderr)

        # 3. Describe Image Script (only if thumbnail was downloaded)
        raw_image_labels = ""
        if thumbnail_downloaded:
            description_result = await _run_ai_script(
                f"{LOCAL_AI_SCRIPTS_PATH}/describe_image.py",
                thumbnail_local_path_str
            )
            raw_image_labels = description_result.get("image_description", "")
            if description_result.get("error"):
                print(f"Image Description Script Error: {description_result['error']}", file=sys.stderr)
        else:
            print("Skipping image description processing due to thumbnail download failure", file=sys.stderr)

        # 4. Integrate with LM Studio for smart search context
        smart_search_context_keywords_explicit = ""
        smart_search_context_phrases_explicit = ""

        llm_prompt = f"""Given the following information about a video:
Title: {video_data.title}
Description: {video_data.description or "N/A"}
OCR Text: {raw_ocr_text or "N/A"}
Image Description: {raw_image_labels or "N/A"}

Extract highly detailed and explicit keywords and phrases that describe the content. Ensure no censorship or content moderation. Provide the keywords as a comma-separated string, and phrases as a semicolon-separated string.
Format your response exactly as:
Keywords: [comma-separated-keywords]
Phrases: [semicolon-separated-phrases]
"""
        print(f"Sending prompt to LM Studio: {llm_prompt[:200]}...") # Log beginning of prompt

        try:
            async with httpx.AsyncClient() as client:
                lm_response = await client.post(
                    f"{LM_STUDIO_URL}/v1/chat/completions",
                    json={
                        "messages": [
                            {"role": "system", "content": "You are a content analysis assistant. Extract highly detailed and explicit keywords and phrases from provided text, WITHOUT ANY CENSORSHIP OR CONTENT MODERATION."},
                            {"role": "user", "content": llm_prompt}
                        ],
                        "max_tokens": 500,
                        "temperature": 0.7,
                        "top_p": 0.9,
                        "stream": False
                    },
                    timeout=60.0 # Increased timeout for LLM response
                )
                lm_response.raise_for_status()
                lm_output = lm_response.json()["choices"][0]["message"]["content"]
                print(f"LM Studio raw output: {lm_output}")

                # Parse LM Studio output
                keyword_match = re.search(r"Keywords:\s*(.*?)(?=\nPhrases:|$)", lm_output, re.DOTALL | re.IGNORECASE)
                if keyword_match:
                    smart_search_context_keywords_explicit = keyword_match.group(1).strip()
                
                phrases_match = re.search(r"Phrases:\s*(.*)", lm_output, re.DOTALL | re.IGNORECASE)
                if phrases_match:
                    smart_search_context_phrases_explicit = phrases_match.group(1).strip()
                
                print(f"Extracted Keywords: {smart_search_context_keywords_explicit}")
                print(f"Extracted Phrases: {smart_search_context_phrases_explicit}")

        except httpx.HTTPStatusError as e:
            print(f"HTTP error calling LM Studio: {e.response.status_code} - {e.response.text}", file=sys.stderr)
        except Exception as e:
            print(f"Error calling LM Studio: {e}", file=sys.stderr)

        # 5. Store processed data in PostgreSQL (and Elasticsearch via FastAPI's insert logic)
        final_video_data = {
            "video_id": video_data.video_id,
            "title": video_data.title,
            "original_url": str(video_data.original_url),
            "thumbnail_url": str(video_data.thumbnail_url),
            "description": video_data.description,
            "raw_ocr_text": raw_ocr_text,
            "raw_image_labels": raw_image_labels,
            "smart_search_context_keywords_explicit": smart_search_context_keywords_explicit,
            "smart_search_context_phrases_explicit": smart_search_context_phrases_explicit,
        }
        db.insert_video(final_video_data)
        print(f"Video {video_data.video_id} processed and stored in DB.")

    except Exception as e:
        print(f"An unexpected error occurred during video processing: {e}", file=sys.stderr)
    finally:
        # Clean up downloaded thumbnail
        if thumbnail_downloaded and 'thumbnail_local_path' in locals() and thumbnail_local_path.exists():
            try:
                os.remove(thumbnail_local_path)
                print(f"Cleaned up temporary thumbnail: {thumbnail_local_path}")
            except Exception as e:
                print(f"Error cleaning up thumbnail {thumbnail_local_path}: {e}", file=sys.stderr)

@app.post("/api/scrape-url", status_code=status.HTTP_202_ACCEPTED)
async def scrape_url_endpoint(
    scrape_request: ScrapeUrlRequest,
    background_tasks: BackgroundTasks
):
    """
    Accepts a URL, scrapes it for video metadata, and processes each found video
    through the existing video processing pipeline.
    """
    print(f"Received URL for scraping: {scrape_request.url}")
    background_tasks.add_task(_scrape_and_process_url, scrape_request.url)
    return {"message": "URL scraping initiated in background.", "url": str(scrape_request.url)}

async def _scrape_and_process_url(url: HttpUrl):
    """
    Scrapes the given URL using advanced browser-based scraper with age verification bypass.
    Falls back to HTTP scraping if advanced scraper fails.
    """
    try:
        print(f"Starting advanced browser-based scraping of URL: {url}")
        
        # Try advanced scraper first
        advanced_success = await _try_advanced_scraper(url)
        
        if not advanced_success:
            print(f"Advanced scraper failed, falling back to HTTP scraping for {url}")
            await _fallback_http_scraper(url)
        
    except Exception as e:
        print(f"Error in scraping pipeline for {url}: {e}", file=sys.stderr)
        # Final fallback to basic HTTP scraping
        await _fallback_http_scraper(url)

async def _try_advanced_scraper(url: HttpUrl) -> bool:
    """
    Attempts to use the advanced Playwright-based scraper.
    Returns True if successful, False if should fall back.
    """
    try:
        from urllib.parse import urlparse
        site_name = urlparse(str(url)).netloc
        
        # Check if advanced_scraper.py exists
        if not os.path.exists('advanced_scraper.py'):
            print("Advanced scraper not found, using fallback method")
            return False
        
        # Run the advanced scraper as a subprocess with timeout
        process = await asyncio.create_subprocess_exec(
            sys.executable, 'advanced_scraper.py', str(url), site_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        try:
            # Wait for process with timeout
            stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=120.0)
        except asyncio.TimeoutError:
            print(f"Advanced scraper timed out for {url}")
            process.kill()
            return False
        
        if process.returncode == 0:
            print(f"Advanced scraper succeeded for {url}")
            output = stdout.decode()
            if output.strip():
                print(f"Advanced scraper output: {output[:500]}...")
            return True
        else:
            error_msg = stderr.decode()
            print(f"Advanced scraper failed for {url}: {error_msg[:200]}")
            return False
            
    except FileNotFoundError:
        print("Python or advanced_scraper.py not found, using fallback method")
        return False
    except Exception as e:
        print(f"Exception in advanced scraper for {url}: {e}")
        return False

async def _fallback_http_scraper(url: HttpUrl):
    """
    Fallback HTTP-based scraping method with enhanced headers.
    """
    try:
        print(f"Using HTTP fallback scraping for: {url}")
        
        # Enhanced headers for better adult site access
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            # Add cookies to bypass age verification where possible
            'Cookie': 'age_verified=1; adult_content=1; age_gate_passed=1; content_filter=off'
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(str(url), headers=headers, timeout=30.0, follow_redirects=True)
            response.raise_for_status()
            html_content = response.text
        
        # Parse HTML and extract video metadata with improved selectors
        videos_found = await _extract_video_metadata(html_content, str(url))
        
        # Process each found video through the existing pipeline
        for video_data in videos_found:
            await _process_video_data(video_data)
        
        print(f"Successfully processed {len(videos_found)} videos from {url} using HTTP fallback")
        
    except httpx.HTTPStatusError as e:
        print(f"HTTP error scraping URL {url}: {e.response.status_code} - {e.response.text}", file=sys.stderr)
    except Exception as e:
        print(f"Error in HTTP fallback scraping {url}: {e}", file=sys.stderr)

async def _fallback_scrape_and_process_url(url: HttpUrl):
    """
    Fallback scraping method using simple HTTP requests.
    """
    try:
        print(f"Using fallback scraping method for: {url}")
        
        # Fetch the webpage with proper headers to bypass anti-bot protection
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(str(url), headers=headers, timeout=30.0, follow_redirects=True)
            response.raise_for_status()
            html_content = response.text
        
        # Parse HTML and extract video metadata
        videos_found = await _extract_video_metadata(html_content, str(url))
        
        # Process each found video through the existing pipeline
        for video_data in videos_found:
            await _process_video_data(video_data)
        
        print(f"Successfully processed {len(videos_found)} videos from {url} using fallback method")
        
    except httpx.HTTPStatusError as e:
        print(f"HTTP error scraping URL {url}: {e.response.status_code} - {e.response.text}", file=sys.stderr)
    except Exception as e:
        print(f"Error scraping URL {url}: {e}", file=sys.stderr)

async def _extract_video_metadata(html_content: str, source_url: str):
    """
    Extracts video metadata from HTML content using BeautifulSoup.
    This is a basic implementation that looks for common video patterns.
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    videos = []
    
    # Enhanced selectors specifically for adult video sites
    video_selectors = [
        # Generic video elements
        'video',
        # Adult site specific patterns
        '.video-block', '.video-item', '.video-card', '.thumb-block',
        '.video-wrap', '.video-container', '.video-player',
        '[class*="video"]', '[id*="video"]',
        '[class*="thumb"]', '[id*="thumb"]',
        '[class*="preview"]', '[id*="preview"]',
        # Thumbnail images that might represent videos
        'img[src*="thumb"]', 'img[src*="preview"]', 'img[src*="screenshot"]',
        'img[data-src*="thumb"]', 'img[data-src*="preview"]',
        # Link elements that might point to videos
        'a[href*=".mp4"]', 'a[href*=".webm"]', 'a[href*=".mov"]',
        'a[href*="video"]', 'a[href*="watch"]', 'a[href*="view"]',
        'a[href*="/v/"]', 'a[href*="/video/"]', 'a[href*="/videos/"]',
        # Common adult site patterns
        '.js-pop', '.wellcome_subbanner', '.dloaddivcol',
        # PornHub specific
        '.phimage', '.videoblock', '.wrap',
        # XVideos specific
        '.mozaique', '.thumb-block', '.thumb',
        # General patterns
        '[data-testid*="video"]', '.item', '.box'
    ]
    
    found_elements = []
    for selector in video_selectors:
        elements = soup.select(selector)
        found_elements.extend(elements)
    
    # Remove duplicates
    unique_elements = list(set(found_elements))
    
    for i, element in enumerate(unique_elements):
        try:
            # Extract basic metadata
            video_id = f"{hash(str(element))}_{i}"  # Generate a unique ID
            
            # Try to find title
            title = ""
            if element.get('title'):
                title = element.get('title')
            elif element.get('alt'):
                title = element.get('alt')
            elif element.text:
                title = element.text.strip()[:100]  # Limit title length
            else:
                # Look for nearby text elements
                parent = element.parent
                if parent:
                    title = parent.get_text().strip()[:100]
            
            if not title:
                title = f"Video from {source_url}"
            
            # Try to find video URL - prioritize actual video page links
            original_url = ""
            
            # Look for video page links more aggressively
            def find_video_link(elem):
                # Check the element itself if it's a link
                if elem.name == 'a' and elem.get('href'):
                    href = elem.get('href')
                    # Only accept URLs that look like video pages, not thumbnails
                    if any(pattern in href for pattern in ['/video/', '/watch/', '/view/', '/v/', '/videos/']):
                        if not any(pattern in href for pattern in ['.jpg', '.png', '.gif', '.webp', 'thumb', 'preview']):
                            return href
                
                # Check parent elements
                current = elem
                for _ in range(3):  # Check up to 3 levels up
                    if current.parent:
                        current = current.parent
                        if current.name == 'a' and current.get('href'):
                            href = current.get('href')
                            if any(pattern in href for pattern in ['/video/', '/watch/', '/view/', '/v/', '/videos/']):
                                if not any(pattern in href for pattern in ['.jpg', '.png', '.gif', '.webp', 'thumb', 'preview']):
                                    return href
                    else:
                        break
                
                # Look for child links
                for link in elem.find_all('a', limit=5):
                    href = link.get('href', '')
                    if href and any(pattern in href for pattern in ['/video/', '/watch/', '/view/', '/v/', '/videos/']):
                        if not any(pattern in href for pattern in ['.jpg', '.png', '.gif', '.webp', 'thumb', 'preview']):
                            return href
                
                # Check for data attributes
                for attr in ['data-video-url', 'data-href', 'data-link']:
                    if elem.get(attr):
                        return elem.get(attr)
                
                return None
            
            original_url = find_video_link(element)
            
            # Make URL absolute if it's relative
            if original_url and not original_url.startswith('http'):
                from urllib.parse import urljoin
                original_url = urljoin(source_url, original_url)
            
            # Skip this element if we can't find a proper video URL
            if not original_url or original_url == source_url or original_url.endswith('/'):
                continue  # Skip this element entirely instead of creating bad URLs
            
            # Try to find thumbnail - much more aggressive approach
            thumbnail_url = ""
            
            def find_thumbnail_url(elem):
                # Check if element itself is an image
                if elem.name == 'img':
                    # Check multiple possible attributes
                    for attr in ['src', 'data-src', 'data-lazy-src', 'data-original', 'data-thumb']:
                        if elem.get(attr):
                            url = elem.get(attr)
                            # Skip tiny images and gifs/icons
                            if not any(x in url.lower() for x in ['1x1', 'pixel', 'spacer', 'icon', 'loading']):
                                return url
                
                # Check if it's a video with poster
                if elem.name == 'video' and elem.get('poster'):
                    return elem.get('poster')
                
                # Look for images in child elements
                for img in elem.find_all('img', limit=5):
                    for attr in ['src', 'data-src', 'data-lazy-src', 'data-original', 'data-thumb']:
                        if img.get(attr):
                            url = img.get(attr)
                            if not any(x in url.lower() for x in ['1x1', 'pixel', 'spacer', 'icon', 'loading']):
                                return url
                
                # Check for background images in style attributes
                style = elem.get('style', '')
                if 'background-image' in style:
                    import re
                    bg_match = re.search(r'background-image:\s*url\(["\']?([^"\']+)["\']?\)', style)
                    if bg_match:
                        return bg_match.group(1)
                
                # Look in parent elements
                current = elem
                for _ in range(2):  # Check up to 2 levels up
                    if current.parent:
                        current = current.parent
                        # Check for images in parent
                        for img in current.find_all('img', limit=3):
                            for attr in ['src', 'data-src', 'data-lazy-src', 'data-original', 'data-thumb']:
                                if img.get(attr):
                                    url = img.get(attr)
                                    if not any(x in url.lower() for x in ['1x1', 'pixel', 'spacer', 'icon', 'loading']):
                                        return url
                    else:
                        break
                
                # Check for data attributes that might contain thumbnail URLs
                for attr in ['data-thumb', 'data-preview', 'data-poster', 'data-image']:
                    if elem.get(attr):
                        return elem.get(attr)
                
                return None
            
            thumbnail_url = find_thumbnail_url(element)
            
            # Make thumbnail URL absolute if it's relative
            if thumbnail_url and not thumbnail_url.startswith('http'):
                from urllib.parse import urljoin
                thumbnail_url = urljoin(source_url, thumbnail_url)
            
            if not thumbnail_url:
                # Use a placeholder thumbnail
                thumbnail_url = "https://via.placeholder.com/280x180?text=No+Thumbnail"
            
            # Try to extract description
            description = ""
            if element.get('data-description'):
                description = element.get('data-description')
            elif element.parent:
                # Look for description in parent or sibling elements
                desc_candidates = element.parent.find_all(['p', 'div', 'span'], limit=3)
                for candidate in desc_candidates:
                    text = candidate.get_text().strip()
                    if len(text) > 20 and len(text) < 500:  # Reasonable description length
                        description = text
                        break
            
            # Create ProcessVideoRequest object
            video_data = ProcessVideoRequest(
                video_id=str(video_id),
                title=title,
                original_url=HttpUrl(original_url),
                thumbnail_url=HttpUrl(thumbnail_url),
                description=description if description else None
            )
            
            videos.append(video_data)
            
        except Exception as e:
            print(f"Error processing video element: {e}", file=sys.stderr)
            continue
    
    print(f"Extracted {len(videos)} videos from HTML content")
    return videos

async def _run_ai_script(script_path: str, image_path: str):
    """
    Helper function to run an external Python AI script as a subprocess.
    """
    try:
        # Ensure the script is executable or called directly with python3
        # Use sys.executable to ensure the correct python executable is used inside the container
        process = await asyncio.create_subprocess_exec(
            sys.executable, script_path, image_path,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            print(f"Script {script_path} failed with error:\n{stderr.decode()}", file=sys.stderr)
            return {"error": stderr.decode()}
        
        try:
            return json.loads(stdout.decode().strip())
        except json.JSONDecodeError:
            print(f"Could not decode JSON from {script_path} output: {stdout.decode()}", file=sys.stderr)
            return {"error": f"Invalid JSON output from script: {stdout.decode()}"}

    except FileNotFoundError:
        return {"error": f"AI script not found at {script_path}. Ensure it's correctly mounted/pathed in Docker."}
    except Exception as e:
        return {"error": f"Failed to run AI script {script_path}: {e}"}

@app.get("/api/stored-sites")
async def get_stored_sites():
    """
    Retrieves all active stored sites for the dropdown.
    """
    sites = db.get_all_stored_sites()
    return {"sites": sites}

@app.post("/api/stored-sites", status_code=status.HTTP_201_CREATED)
async def add_stored_site(site_data: StoredSiteRequest):
    """
    Adds a new stored site to the database.
    """
    site_dict = {
        "site_name": site_data.site_name,
        "site_url": str(site_data.site_url),
        "description": site_data.description,
        "category": site_data.category
    }
    
    site_id = db.insert_stored_site(site_dict)
    if site_id:
        return {"message": "Site added successfully", "site_id": site_id}
    else:
        raise HTTPException(status_code=400, detail="Failed to add site")

@app.delete("/api/stored-sites/{site_id}")
async def delete_stored_site(site_id: int):
    """
    Deletes a stored site by marking it as inactive.
    """
    success = db.delete_stored_site(site_id)
    if success:
        return {"message": "Site deleted successfully"}
    else:
        raise HTTPException(status_code=404, detail="Site not found")

@app.post("/api/scrape-stored-site/{site_id}", status_code=status.HTTP_202_ACCEPTED)
async def scrape_stored_site(site_id: int, background_tasks: BackgroundTasks):
    """
    Scrapes a stored site by its ID.
    """
    sites = db.get_all_stored_sites()
    site = next((s for s in sites if s['site_id'] == site_id), None)
    
    if not site:
        raise HTTPException(status_code=404, detail="Stored site not found")
    
    print(f"Scraping stored site: {site['site_name']} ({site['site_url']})")
    background_tasks.add_task(_scrape_and_process_url, site['site_url'])
    return {"message": f"Scraping initiated for {site['site_name']}", "url": site['site_url']}

@app.post("/api/populate-sites", status_code=status.HTTP_201_CREATED)
async def populate_adult_sites():
    """
    Populates the database with major adult video platforms.
    """
    adult_sites = [
        # Major Free Adult Video Platforms
        {"site_name": "Pornhub", "site_url": "https://www.pornhub.com", "description": "Major adult video platform", "category": "video"},
        {"site_name": "XVideos", "site_url": "https://www.xvideos.com", "description": "Popular adult video site", "category": "video"},
        {"site_name": "XNXX", "site_url": "https://www.xnxx.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "YouPorn", "site_url": "https://www.youporn.com", "description": "Adult video sharing site", "category": "video"},
        {"site_name": "RedTube", "site_url": "https://www.redtube.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "Tube8", "site_url": "https://www.tube8.com", "description": "Adult video site", "category": "video"},
        {"site_name": "SpankBang", "site_url": "https://spankbang.com", "description": "Adult video platform with young content", "category": "video"},
        {"site_name": "Beeg", "site_url": "https://beeg.com", "description": "Adult video site", "category": "video"},
        {"site_name": "XHamster", "site_url": "https://xhamster.com", "description": "Adult video and community", "category": "video"},
        {"site_name": "PornTrex", "site_url": "https://www.porntrex.com", "description": "Adult video aggregator", "category": "video"},
        {"site_name": "ThisVid", "site_url": "https://thisvid.com", "description": "Adult video sharing platform", "category": "video"},
        {"site_name": "Motherless", "site_url": "https://motherless.com", "description": "User-generated adult content", "category": "video"},
        {"site_name": "Heavy-R", "site_url": "https://heavy-r.com", "description": "Extreme adult content", "category": "video"},
        {"site_name": "Empflix", "site_url": "https://www.empflix.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "DrTuber", "site_url": "https://www.drtuber.com", "description": "Adult video site", "category": "video"},
        {"site_name": "AlohaTube", "site_url": "https://www.alohatube.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "PornOne", "site_url": "https://pornone.com", "description": "Adult video aggregator", "category": "video"},
        {"site_name": "Porn.com", "site_url": "https://www.porn.com", "description": "Adult video aggregator", "category": "video"},
        {"site_name": "Fuq", "site_url": "https://www.fuq.com", "description": "Adult video search", "category": "video"},
        {"site_name": "NudeVista", "site_url": "https://www.nudevista.com", "description": "Adult content search", "category": "video"},
        {"site_name": "Pornktube", "site_url": "https://www.pornktube.com", "description": "Adult video platform", "category": "video"},
        {"site_name": "Eporner", "site_url": "https://www.eporner.com", "description": "Adult videos and images", "category": "video"},
        
        # Premium Sites
        {"site_name": "Brazzers", "site_url": "https://www.brazzers.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Reality Kings", "site_url": "https://www.realitykings.com", "description": "Premium adult videos", "category": "premium"},
        {"site_name": "Naughty America", "site_url": "https://www.naughtyamerica.com", "description": "Premium adult content", "category": "premium"},
        {"site_name": "Team Skeet", "site_url": "https://www.teamskeet.com", "description": "Premium young adult content", "category": "premium"},
        {"site_name": "18 Years Old", "site_url": "https://www.18yearsold.com", "description": "Young adult premium content", "category": "premium"},
        {"site_name": "Teen Mega World", "site_url": "https://www.teenmegaworld.net", "description": "Teen premium content", "category": "premium"}
    ]
    
    success_count = 0
    for site in adult_sites:
        try:
            site_id = db.insert_stored_site(site)
            if site_id:
                success_count += 1
        except Exception as e:
            print(f"Error adding {site['site_name']}: {e}")
    
    return {"message": f"Successfully added {success_count}/{len(adult_sites)} sites to database."}